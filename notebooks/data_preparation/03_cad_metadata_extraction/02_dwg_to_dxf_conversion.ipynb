{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Discover DWG files and facilitate their conversion to DXF format:\n", "- Locate all DWG files in the project data structure\n", "- Validate file accessibility and basic properties\n", "- Provide conversion guidance and automation where possible\n", "- Generate file inventory for tracking conversion progress"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Recursively discover DWG files using cross-platform file operations\n", "2. Validate file accessibility and basic properties\n", "3. Check for existing DXF conversions\n", "4. Provide conversion instructions and automation scripts\n", "5. Generate progress tracking and file management tools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import os\n", "import subprocess\n", "from pathlib import Path\n", "from collections import defaultdict\n", "from datetime import datetime\n", "from typing import List, Dict, Optional, Tuple\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"DWG File Discovery and DXF Conversion - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths for DWG file discovery\n", "project_root = Path('../../../')  # Adjust to project root from notebooks/preprocessing/dwg_to_dxf/\n", "data_path = project_root / 'data'\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"Data path: {data_path.resolve()}\")\n", "\n", "# Verify the data directory exists\n", "if not data_path.exists():\n", "    logger.error(f\"Data directory does not exist: {data_path}\")\n", "    raise FileNotFoundError(f\"Directory not found: {data_path}\")\n", "elif not data_path.is_dir():\n", "    logger.error(f\"Data path is not a directory: {data_path}\")\n", "    raise NotADirectoryError(f\"Path is not a directory: {data_path}\")\n", "else:\n", "    logger.info(f\"Data directory verified: {data_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# DWG file discovery\n", "discovered_dwg_files = []\n", "error_files = []\n", "subdirectory_counts = defaultdict(int)\n", "conversion_status = {}\n", "\n", "print(\"Starting DWG file discovery...\")\n", "logger.info(\"Beginning recursive DWG file discovery\")\n", "\n", "try:\n", "    # Use rglob for recursive discovery of DWG files\n", "    dwg_pattern = \"*.dwg\"\n", "    dwg_files = data_path.rglob(dwg_pattern)\n", "    \n", "    for dwg_file in dwg_files:\n", "        try:\n", "            # Verify file is readable and has correct extension\n", "            if dwg_file.is_file() and dwg_file.suffix.lower() == '.dwg':\n", "                # Check if file is readable\n", "                if os.access(dwg_file, os.R_OK):\n", "                    discovered_dwg_files.append(dwg_file)\n", "                    \n", "                    # Count files by subdirectory\n", "                    relative_path = dwg_file.relative_to(data_path)\n", "                    subdirectory = relative_path.parts[0] if len(relative_path.parts) > 1 else 'root'\n", "                    subdirectory_counts[subdirectory] += 1\n", "                    \n", "                    # Check if corresponding DXF file exists\n", "                    dxf_file = dwg_file.with_suffix('.dxf')\n", "                    conversion_status[str(dwg_file)] = {\n", "                        'dwg_path': str(dwg_file),\n", "                        'dxf_path': str(dxf_file),\n", "                        'dxf_exists': dxf_file.exists(),\n", "                        'dwg_size_mb': dwg_file.stat().st_size / (1024 * 1024),\n", "                        'dxf_size_mb': dxf_file.stat().st_size / (1024 * 1024) if dxf_file.exists() else 0,\n", "                        'needs_conversion': not dxf_file.exists()\n", "                    }\n", "                    \n", "                    logger.debug(f\"Discovered DWG: {dwg_file}\")\n", "                else:\n", "                    error_files.append((dwg_file, \"File not readable\"))\n", "                    logger.warning(f\"DWG file not readable: {dwg_file}\")\n", "            else:\n", "                error_files.append((dwg_file, \"Invalid file type or not a file\"))\n", "                logger.warning(f\"Invalid DWG file: {dwg_file}\")\n", "                \n", "        except Exception as e:\n", "            error_files.append((dwg_file, str(e)))\n", "            logger.error(f\"Error processing file {dwg_file}: {e}\")\n", "            \n", "except Exception as e:\n", "    logger.error(f\"Error during DWG discovery: {e}\")\n", "    raise\n", "\n", "logger.info(f\"DWG discovery completed. Found {len(discovered_dwg_files)} valid DWG files\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for available conversion tools\n", "def check_conversion_tools() -> Dict[str, bool]:\n", "    \"\"\"Check for available DWG to DXF conversion tools.\"\"\"\n", "    tools = {\n", "        'teigha_file_converter': False,\n", "        'dwg2dxf': <PERSON><PERSON><PERSON>,\n", "        'librecad': <PERSON><PERSON><PERSON>,\n", "        'qcad': False\n", "    }\n", "    \n", "    # Check for Teigha File Converter (ODA)\n", "    try:\n", "        result = subprocess.run(['ODAFileConverter'], capture_output=True, text=True, timeout=5)\n", "        if result.returncode == 0 or 'ODA File Converter' in result.stderr:\n", "            tools['teigha_file_converter'] = True\n", "    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):\n", "        pass\n", "    \n", "    # Check for other tools (add more as needed)\n", "    tool_commands = {\n", "        'dwg2dxf': ['dwg2dxf', '--version'],\n", "        'librecad': ['librecad', '--version'],\n", "        'qcad': ['qcad', '--version']\n", "    }\n", "    \n", "    for tool_name, command in tool_commands.items():\n", "        try:\n", "            result = subprocess.run(command, capture_output=True, text=True, timeout=5)\n", "            if result.returncode == 0:\n", "                tools[tool_name] = True\n", "        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):\n", "            pass\n", "    \n", "    return tools\n", "\n", "print(\"\\nChecking for available conversion tools...\")\n", "available_tools = check_conversion_tools()\n", "\n", "print(\"Available conversion tools:\")\n", "for tool, available in available_tools.items():\n", "    status = \"✓ Available\" if available else \"✗ Not found\"\n", "    print(f\"  {tool}: {status}\")\n", "\n", "if not any(available_tools.values()):\n", "    print(\"\\nNo automatic conversion tools found.\")\n", "    print(\"Manual conversion or tool installation may be required.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display DWG discovery summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"DWG FILE DISCOVERY SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nTotal DWG files discovered: {len(discovered_dwg_files)}\")\n", "print(f\"Files with errors: {len(error_files)}\")\n", "\n", "# Conversion status summary\n", "files_needing_conversion = sum(1 for status in conversion_status.values() if status['needs_conversion'])\n", "files_already_converted = len(conversion_status) - files_needing_conversion\n", "\n", "print(f\"\\nConversion Status:\")\n", "print(f\"  Files already converted to DXF: {files_already_converted}\")\n", "print(f\"  Files needing conversion: {files_needing_conversion}\")\n", "\n", "if subdirectory_counts:\n", "    print(\"\\nFiles by subdirectory:\")\n", "    for subdirectory, count in sorted(subdirectory_counts.items()):\n", "        print(f\"  {subdirectory}: {count} files\")\n", "\n", "if error_files:\n", "    print(\"\\nFiles with errors:\")\n", "    for error_file, error_msg in error_files:\n", "        print(f\"  {error_file}: {error_msg}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"DWG DISCOVERY AND CONVERSION SETUP COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if discovered_dwg_files:\n", "    print(f\"Successfully discovered {len(discovered_dwg_files)} DWG files\")\n", "    \n", "    files_needing_conversion = sum(1 for status in conversion_status.values() if status['needs_conversion'])\n", "    if files_needing_conversion > 0:\n", "        print(f\"{files_needing_conversion} files need conversion to DXF\")\n", "        print(\"Conversion scripts and instructions have been generated\")\n", "    else:\n", "        print(\"All DWG files already have corresponding DXF files\")\n", "    \n", "    print(\"Ready for DXF processing workflows\")\n", "else:\n", "    print(\"No DWG files discovered - please verify data directory structure\")\n", "\n", "if any(available_tools.values()):\n", "    available_tool_names = [name for name, available in available_tools.items() if available]\n", "    print(f\"\\nAvailable conversion tools: {', '.join(available_tool_names)}\")\n", "else:\n", "    print(\"\\nNo automatic conversion tools found.\")\n", "    print(\"Please install ODA File Converter or use manual conversion methods.\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"DWG discovery and conversion setup notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}
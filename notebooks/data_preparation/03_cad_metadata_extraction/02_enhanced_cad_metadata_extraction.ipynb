{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhanced CAD Metadata Extraction for Alignment and Labeling\n", "\n", "This notebook provides comprehensive CAD metadata extraction from DXF and DWG files for downstream alignment and labeling workflows.\n", "\n", "**Stage**: Data Preparation - CAD Metadata Extraction  \n", "**Input Data**: DXF/DWG files from CAD directory  \n", "**Output**: Structured metadata in CSV/JSON format following metadata schema  \n", "**Purpose**: Extract geometric entities, coordinates, and annotations for point cloud alignment and labeling  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DXF Entity Types Reference Guide\n", "\n", "Based on AutoCAD DXF Reference and common entities found in solar project CAD files:\n", "\n", "### Geometric Entities\n", "- **CIRCLE**: Circular arcs and circles (often used for piles, manholes)\n", "- **LINE**: Straight line segments (boundaries, connections, infrastructure)\n", "- **LWPOLYLINE**: Lightweight polylines (complex shapes, site boundaries, roads)\n", "- **POLYLINE**: 3D polylines with vertices (terrain contours, complex paths)\n", "- **ARC**: Circular arc segments (curved roads, rounded corners)\n", "- **POINT**: Single coordinate points (survey points, reference markers)\n", "\n", "### Block and Text Entities\n", "- **INSERT**: Block references (solar panels, equipment, symbols)\n", "- **TEXT**: Single-line text (labels, dimensions, annotations)\n", "- **MTEXT**: Multi-line text (descriptions, notes, specifications)\n", "- **ATTDEF**: Attribute definitions in blocks\n", "- **DIMENSION**: Dimension lines and text\n", "\n", "### Surface and Fill Entities\n", "- **HATCH**: Filled areas and patterns (buildings, water bodies, zones)\n", "- **SOLID**: Solid filled triangular areas\n", "- **TRACE**: Solid filled quadrilateral areas\n", "\n", "### Advanced Entities\n", "- **SPLINE**: Smooth curves (organic shapes, terrain features)\n", "- **ELLIPSE**: Elliptical shapes\n", "- **VIEWPORT**: Layout viewports\n", "- **IMAGE**: <PERSON><PERSON> image references\n", "- **OLE2FRAME**: Embedded OLE objects\n", "- **PDFREFERENCE**: PDF underlay references\n", "\n", "**Reference**: [AutoCAD DXF Reference](https://help.autodesk.com/view/OARX/2023/ENU/?guid=GUID-235B22E0-A567-4CF6-92D3-38A2306D73F3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Extract comprehensive metadata from CAD files to support:\n", "- Point cloud to CAD alignment workflows\n", "- Automated labeling of point cloud features\n", "- Coordinate system transformation and validation\n", "- Geometric feature detection and classification\n", "- Foundation, pile, and infrastructure element identification"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. **File Discovery**: Locate and validate DXF/DWG files\n", "2. **Entity Extraction**: Extract geometric entities with full metadata\n", "3. **Classification**: Intelligent categorization of CAD entities\n", "4. **Coordinate Processing**: Handle coordinate systems and transformations\n", "5. **Structured Output**: Generate standardized metadata following schema\n", "6. **Quality Validation**: Verify extraction completeness and accuracy"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"motali_de_castro\"\n", "site_name = \"main_site\"\n", "cad_data_path = \"../../../data/raw/motali_de_castro/cad\"\n", "output_dir = \"../../../output_runs/cad_metadata\"\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\", \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"]\n", "coordinate_system = \"EPSG:32633\"  # UTM Zone 33N for Italy\n", "classification_rules_file = None  # Optional custom classification rules"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import logging\n", "import json\n", "import csv\n", "from pathlib import Path\n", "from datetime import datetime\n", "from typing import Dict, List, Tuple, Optional, Any\n", "from collections import defaultdict\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import ezdxf\n", "from ezdxf import recover\n", "import mlflow"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-30 15:43:19,713 - INFO - Enhanced CAD Metadata Extraction - Starting...\n", "2025-06-30 15:43:19,714 - INFO - Timestamp: 2025-06-30 15:43:19\n", "2025-06-30 15:43:19,715 - INFO - Project: motali_de_castro/main_site\n"]}], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(\"Enhanced CAD Metadata Extraction - Starting...\")\n", "logger.info(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(f\"Project: {project_type}/{site_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Configuration"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-30 15:43:33,532 - INFO - CAD data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/motali_de_castro/cad\n", "2025-06-30 15:43:33,533 - INFO - Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/output_runs/cad_metadata/motali_de_castro_main_site_20250630_154333\n", "2025-06-30 15:43:33,534 - INFO - Setup completed - Output directory: ../../../output_runs/cad_metadata/motali_de_castro_main_site_20250630_154333\n"]}], "source": ["# Setup paths and directories\n", "cad_path = Path(cad_data_path)\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Create timestamped output directory\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "run_output_dir = output_path / f\"{project_type}_{site_name}_{timestamp}\"\n", "run_output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"CAD data path: {cad_path.resolve()}\")\n", "logger.info(f\"Output directory: {run_output_dir.resolve()}\")\n", "\n", "# Verify CAD directory exists\n", "if not cad_path.exists():\n", "    raise FileNotFoundError(f\"CAD directory not found: {cad_path}\")\n", "\n", "logger.info(f\"Setup completed - Output directory: {run_output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced CAD Entity Classification System"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class CADEntityClassifier:\n", "    \"\"\"Enhanced classification system for CAD entities.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.classification_rules = {\n", "            'pile': {\n", "                'layer_keywords': ['pile', 'palo', 'pali'],\n", "                'block_keywords': ['pile', 'palo', 'p_'],\n", "                'text_keywords': ['pile', 'palo', 'p-'],\n", "                'entity_types': ['CIRCLE', 'INSERT', 'POINT']\n", "            },\n", "            'panel': {\n", "                'layer_keywords': ['panel', 'pv', 'solar', 'modulo', 'pannello'],\n", "                'block_keywords': ['panel', 'pv', 'solar', 'modulo'],\n", "                'text_keywords': ['panel', 'pv', 'solar'],\n", "                'entity_types': ['INSERT', 'LWPOLYLINE', 'POLYLINE']\n", "            },\n", "            'road': {\n", "                'layer_keywords': ['road', 'strada', 'strade', 'access', 'accesso'],\n", "                'block_keywords': ['road', 'strada'],\n", "                'text_keywords': ['road', 'strada'],\n", "                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE', 'ARC']\n", "            },\n", "            'trench': {\n", "                'layer_keywords': ['trench', 'trincea', 'cable', 'cavo', 'cavidotto'],\n", "                'block_keywords': ['trench', 'cable'],\n", "                'text_keywords': ['trench', 'cable', 'cavo'],\n", "                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE']\n", "            },\n", "            'foundation': {\n", "                'layer_keywords': ['foundation', 'fondazione', 'base', 'cabin', 'cabina'],\n", "                'block_keywords': ['foundation', 'cabin', 'base'],\n", "                'text_keywords': ['foundation', 'cabin', 'base'],\n", "                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT']\n", "            },\n", "            'electrical': {\n", "                'layer_keywords': ['electrical', 'elettrico', 'power', 'energia'],\n", "                'block_keywords': ['electrical', 'power'],\n", "                'text_keywords': ['electrical', 'power', 'kw', 'v'],\n", "                'entity_types': ['LINE', 'LWPOLYLINE', 'INSERT']\n", "            },\n", "            'annotation': {\n", "                'layer_keywords': ['text', 'label', 'annotation', 'quota', 'dimension'],\n", "                'block_keywords': ['text', 'label'],\n", "                'text_keywords': [],\n", "                'entity_types': ['TEXT', 'MTEXT', 'DIMENSION']\n", "            }\n", "        }\n", "    \n", "    def classify_entity(self, entity, layer_name: str = \"\", block_name: str = \"\", text_content: str = \"\") -> str:\n", "        \"\"\"Classify CAD entity based on multiple criteria.\"\"\"\n", "        entity_type = entity.dxftype()\n", "        layer_name = layer_name.lower()\n", "        block_name = block_name.lower()\n", "        text_content = text_content.lower()\n", "        \n", "        scores = defaultdict(int)\n", "        \n", "        for category, rules in self.classification_rules.items():\n", "            # Check entity type match\n", "            if entity_type in rules['entity_types']:\n", "                scores[category] += 2\n", "            \n", "            # Check layer keywords\n", "            for keyword in rules['layer_keywords']:\n", "                if keyword in layer_name:\n", "                    scores[category] += 3\n", "            \n", "            # Check block keywords\n", "            for keyword in rules['block_keywords']:\n", "                if keyword in block_name:\n", "                    scores[category] += 3\n", "            \n", "            # Check text keywords\n", "            for keyword in rules['text_keywords']:\n", "                if keyword in text_content:\n", "                    scores[category] += 2\n", "        \n", "        # Return highest scoring category or 'unknown'\n", "        if scores:\n", "            return max(scores.items(), key=lambda x: x[1])[0]\n", "        return 'unknown'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced Entity Extraction Functions"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def extract_comprehensive_entity_data(entity, classifier: CADEntityClassifier) -> Dict[str, Any]:\n", "    \"\"\"Extract comprehensive metadata from CAD entity.\"\"\"\n", "    try:\n", "        entity_type = entity.dxftype()\n", "        handle = getattr(entity.dxf, 'handle', 'unknown')\n", "        layer_name = getattr(entity.dxf, 'layer', '')\n", "        \n", "        # Base entity data\n", "        entity_data = {\n", "            'entity_id': handle,\n", "            'entity_type': entity_type,\n", "            'layer_name': layer_name,\n", "            'color': getattr(entity.dxf, 'color', None),\n", "            'linetype': getattr(entity.dxf, 'linetype', None),\n", "            'lineweight': getattr(entity.dxf, 'lineweight', None)\n", "        }\n", "        \n", "        # Extract geometry and coordinates based on entity type\n", "        if entity_type == 'CIRCLE':\n", "            center = entity.dxf.center\n", "            entity_data.update({\n", "                'x_coord': center[0],\n", "                'y_coord': center[1],\n", "                'z_coord': center[2] if len(center) > 2 else 0.0,\n", "                'radius': entity.dxf.radius,\n", "                'geometry_type': 'circle'\n", "            })\n", "            \n", "        elif entity_type == 'LINE':\n", "            start = entity.dxf.start\n", "            end = entity.dxf.end\n", "            entity_data.update({\n", "                'x_coord': (start[0] + end[0]) / 2,  # Midpoint\n", "                'y_coord': (start[1] + end[1]) / 2,\n", "                'z_coord': (start[2] + end[2]) / 2 if len(start) > 2 else 0.0,\n", "                'start_x': start[0],\n", "                'start_y': start[1],\n", "                'start_z': start[2] if len(start) > 2 else 0.0,\n", "                'end_x': end[0],\n", "                'end_y': end[1],\n", "                'end_z': end[2] if len(end) > 2 else 0.0,\n", "                'length': np.linalg.norm(np.array(end) - np.array(start)),\n", "                'geometry_type': 'line'\n", "            })\n", "            \n", "        elif entity_type == 'INSERT':\n", "            insert_point = entity.dxf.insert\n", "            entity_data.update({\n", "                'x_coord': insert_point[0],\n", "                'y_coord': insert_point[1],\n", "                'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,\n", "                'block_name': entity.dxf.name,\n", "                'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                'scale_x': getattr(entity.dxf, 'xscale', 1.0),\n", "                'scale_y': getattr(entity.dxf, 'yscale', 1.0),\n", "                'scale_z': getattr(entity.dxf, 'zscale', 1.0),\n", "                'geometry_type': 'insert'\n", "            })\n", "            \n", "        elif entity_type in ['TEXT', 'MTEXT']:\n", "            insert_point = entity.dxf.insert\n", "            text_content = entity.dxf.text if entity_type == 'TEXT' else entity.text\n", "            entity_data.update({\n", "                'x_coord': insert_point[0],\n", "                'y_coord': insert_point[1],\n", "                'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,\n", "                'text_content': text_content,\n", "                'text_height': getattr(entity.dxf, 'height', None),\n", "                'text_rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                'geometry_type': 'text'\n", "            })\n", "            \n", "        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:\n", "            # Get polyline points\n", "            if hasattr(entity, 'get_points'):\n", "                points = list(entity.get_points())\n", "            else:\n", "                points = []\n", "            \n", "            if points:\n", "                # Calculate centroid\n", "                points_array = np.array(points)\n", "                centroid = np.mean(points_array, axis=0)\n", "                entity_data.update({\n", "                    'x_coord': centroid[0],\n", "                    'y_coord': centroid[1],\n", "                    'z_coord': centroid[2] if len(centroid) > 2 else 0.0,\n", "                    'point_count': len(points),\n", "                    'is_closed': getattr(entity.dxf, 'flags', 0) & 1,\n", "                    'geometry_type': 'polyline'\n", "                })\n", "            else:\n", "                entity_data.update({\n", "                    'x_coord': 0.0,\n", "                    'y_coord': 0.0,\n", "                    'z_coord': 0.0,\n", "                    'geometry_type': 'polyline'\n", "                })\n", "        \n", "        else:\n", "            # Default handling for other entity types\n", "            entity_data.update({\n", "                'x_coord': 0.0,\n", "                'y_coord': 0.0,\n", "                'z_coord': 0.0,\n", "                'geometry_type': 'other'\n", "            })\n", "        \n", "        # Classify entity\n", "        block_name = entity_data.get('block_name', '')\n", "        text_content = entity_data.get('text_content', '')\n", "        entity_data['classification'] = classifier.classify_entity(\n", "            entity, layer_name, block_name, text_content\n", "        )\n", "        \n", "        # Add extraction metadata\n", "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n", "        \n", "        return entity_data\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Error extracting entity data: {e}\")\n", "        return {\n", "            'entity_id': 'error',\n", "            'entity_type': getattr(entity, 'dxftype', lambda: 'unknown')(),\n", "            'error': str(e),\n", "            'extraction_timestamp': datetime.now().isoformat()\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Processing Functions"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def process_dxf_file(file_path: Path, classifier: CADEntityClassifier) -> Tuple[List[Dict], Dict]:\n", "    \"\"\"Process a single DXF file and extract all entity metadata.\"\"\"\n", "    logger.info(f\"Processing DXF file: {file_path.name}\")\n", "    \n", "    entities_data = []\n", "    file_stats = {\n", "        'file_name': file_path.name,\n", "        'file_path': str(file_path),\n", "        'file_size_mb': file_path.stat().st_size / (1024 * 1024),\n", "        'processing_timestamp': datetime.now().isoformat(),\n", "        'entity_counts': defaultdict(int),\n", "        'classification_counts': defaultdict(int),\n", "        'layer_counts': defaultdict(int),\n", "        'coordinate_bounds': {'min_x': float('inf'), 'max_x': float('-inf'),\n", "                             'min_y': float('inf'), 'max_y': float('-inf')},\n", "        'errors': []\n", "    }\n", "    \n", "    try:\n", "        # Try to read the file, use recovery if needed\n", "        try:\n", "            doc = ezdxf.readfile(file_path)\n", "        except ezdxf.DXFStructureError:\n", "            logger.warning(f\"DXF structure error, attempting recovery: {file_path.name}\")\n", "            doc, auditor = recover.readfile(file_path)\n", "            if auditor.has_errors:\n", "                file_stats['errors'].extend([str(error) for error in auditor.errors])\n", "        \n", "        # Process modelspace entities\n", "        msp = doc.modelspace()\n", "        logger.info(f\"Processing {len(msp)} entities in modelspace\")\n", "        \n", "        for entity in msp:\n", "            entity_data = extract_comprehensive_entity_data(entity, classifier)\n", "            entity_data['source_file'] = file_path.name\n", "            entity_data['source_space'] = 'modelspace'\n", "            entities_data.append(entity_data)\n", "            \n", "            # Update statistics\n", "            file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1\n", "            file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1\n", "            file_stats['layer_counts'][entity_data.get('layer_name', 'unknown')] += 1\n", "            \n", "            # Update coordinate bounds\n", "            x_coord = entity_data.get('x_coord')\n", "            y_coord = entity_data.get('y_coord')\n", "            if x_coord is not None and y_coord is not None:\n", "                file_stats['coordinate_bounds']['min_x'] = min(file_stats['coordinate_bounds']['min_x'], x_coord)\n", "                file_stats['coordinate_bounds']['max_x'] = max(file_stats['coordinate_bounds']['max_x'], x_coord)\n", "                file_stats['coordinate_bounds']['min_y'] = min(file_stats['coordinate_bounds']['min_y'], y_coord)\n", "                file_stats['coordinate_bounds']['max_y'] = max(file_stats['coordinate_bounds']['max_y'], y_coord)\n", "        \n", "        # Process block definitions\n", "        logger.info(f\"Processing {len(doc.blocks)} block definitions\")\n", "        for block in doc.blocks:\n", "            if block.name.startswith('*'):  # Skip anonymous blocks\n", "                continue\n", "                \n", "            for entity in block:\n", "                entity_data = extract_comprehensive_entity_data(entity, classifier)\n", "                entity_data['source_file'] = file_path.name\n", "                entity_data['source_space'] = f'block:{block.name}'\n", "                entities_data.append(entity_data)\n", "                \n", "                # Update statistics\n", "                file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1\n", "                file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1\n", "        \n", "        file_stats['total_entities'] = len(entities_data)\n", "        file_stats['processing_status'] = 'success'\n", "        \n", "        logger.info(f\"Successfully processed {file_path.name}: {len(entities_data)} entities\")\n", "        \n", "    except Exception as e:\n", "        error_msg = f\"Error processing {file_path.name}: {str(e)}\"\n", "        logger.error(error_msg)\n", "        file_stats['errors'].append(error_msg)\n", "        file_stats['processing_status'] = 'error'\n", "    \n", "    return entities_data, file_stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Processing Pipeline"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025/06/30 15:44:23 INFO mlflow.tracking.fluent: Experiment with name 'cad_metadata_extraction_motali_de_castro' does not exist. Creating a new experiment.\n", "2025-06-30 15:44:23,365 - INFO - Processing DXF file: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== File Discovery ===\n", "Found target file: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "Found target file: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\n", "\n", "Total files to process: 2\n", "\n", "=== Processing Files ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-30 15:44:23,996 - INFO - Processing 1065 entities in modelspace\n", "2025-06-30 15:44:24,014 - INFO - Processing 34 block definitions\n", "2025-06-30 15:44:24,048 - INFO - Successfully processed GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf: 4387 entities\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Skipping non-DXF file: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg (DWG processing requires conversion)\n", "\n", "=== Processing Summary ===\n", "Total files processed: 2\n", "Successful: 1\n", "Failed: 0\n", "Total entities extracted: 4387\n"]}], "source": ["# Initialize classifier\n", "classifier = CADEntityClassifier()\n", "\n", "# Initialize MLflow tracking\n", "mlflow.set_experiment(f\"cad_metadata_extraction_{project_type}\")\n", "\n", "with mlflow.start_run(run_name=f\"{site_name}_cad_extraction_{timestamp}\"):\n", "    # Log parameters\n", "    mlflow.log_param(\"project_type\", project_type)\n", "    mlflow.log_param(\"site_name\", site_name)\n", "    mlflow.log_param(\"coordinate_system\", coordinate_system)\n", "    mlflow.log_param(\"target_files\", \",\".join(target_files))\n", "    \n", "    # Discover CAD files\n", "    print(\"\\n=== File Discovery ===\")\n", "    discovered_files = []\n", "    \n", "    if target_files:\n", "        # Process specific target files\n", "        for target_file in target_files:\n", "            file_path = cad_path / target_file\n", "            if file_path.exists() and file_path.suffix.lower() in ['.dxf', '.dwg']:\n", "                discovered_files.append(file_path)\n", "                print(f\"Found target file: {target_file}\")\n", "            else:\n", "                print(f\"Target file not found or invalid: {target_file}\")\n", "    else:\n", "        # Discover all DXF files in directory\n", "        for file_path in cad_path.rglob(\"*.dxf\"):\n", "            discovered_files.append(file_path)\n", "            print(f\"Discovered DXF file: {file_path.name}\")\n", "    \n", "    print(f\"\\nTotal files to process: {len(discovered_files)}\")\n", "    mlflow.log_metric(\"files_discovered\", len(discovered_files))\n", "    \n", "    # Process each file\n", "    all_entities = []\n", "    all_file_stats = []\n", "    processing_summary = {\n", "        'total_files': len(discovered_files),\n", "        'successful_files': 0,\n", "        'failed_files': 0,\n", "        'total_entities': 0,\n", "        'classification_summary': defaultdict(int),\n", "        'entity_type_summary': defaultdict(int)\n", "    }\n", "    \n", "    print(\"\\n=== Processing Files ===\")\n", "    for file_path in discovered_files:\n", "        if file_path.suffix.lower() == '.dxf':\n", "            entities_data, file_stats = process_dxf_file(file_path, classifier)\n", "            \n", "            all_entities.extend(entities_data)\n", "            all_file_stats.append(file_stats)\n", "            \n", "            if file_stats['processing_status'] == 'success':\n", "                processing_summary['successful_files'] += 1\n", "                processing_summary['total_entities'] += len(entities_data)\n", "                \n", "                # Update summaries\n", "                for classification, count in file_stats['classification_counts'].items():\n", "                    processing_summary['classification_summary'][classification] += count\n", "                for entity_type, count in file_stats['entity_counts'].items():\n", "                    processing_summary['entity_type_summary'][entity_type] += count\n", "            else:\n", "                processing_summary['failed_files'] += 1\n", "        else:\n", "            print(f\"Skipping non-DXF file: {file_path.name} (DWG processing requires conversion)\")\n", "    \n", "    print(f\"\\n=== Processing Summary ===\")\n", "    print(f\"Total files processed: {processing_summary['total_files']}\")\n", "    print(f\"Successful: {processing_summary['successful_files']}\")\n", "    print(f\"Failed: {processing_summary['failed_files']}\")\n", "    print(f\"Total entities extracted: {processing_summary['total_entities']}\")\n", "    \n", "    # Log metrics to MLflow\n", "    mlflow.log_metric(\"files_processed\", processing_summary['total_files'])\n", "    mlflow.log_metric(\"files_successful\", processing_summary['successful_files'])\n", "    mlflow.log_metric(\"files_failed\", processing_summary['failed_files'])\n", "    mlflow.log_metric(\"total_entities\", processing_summary['total_entities'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Structured Output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Generating Structured Output ===\n", "Entities CSV saved: motali_de_castro_main_site_cad_entities.csv\n", "Panel entities CSV saved: motali_de_castro_main_site_cad_panel.csv (529 entities)\n", "Road entities CSV saved: motali_de_castro_main_site_cad_road.csv (815 entities)\n", "Annotation entities CSV saved: motali_de_castro_main_site_cad_annotation.csv (1019 entities)\n", "Pile entities CSV saved: motali_de_castro_main_site_cad_pile.csv (1350 entities)\n", "Foundation entities CSV saved: motali_de_castro_main_site_cad_foundation.csv (14 entities)\n", "Electrical entities CSV saved: motali_de_castro_main_site_cad_electrical.csv (35 entities)\n", "File statistics CSV saved: motali_de_castro_main_site_cad_file_stats.csv\n", "Processing summary JSON saved: motali_de_castro_main_site_cad_summary.json\n"]}], "source": ["# Create comprehensive DataFrame\n", "if all_entities:\n", "        print(\"\\n=== Generating Structured Output ===\")\n", "        \n", "        # Convert to DataFrame\n", "        entities_df = pd.DataFrame(all_entities)\n", "        \n", "        # Save main entities CSV\n", "        entities_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_entities.csv\"\n", "        entities_df.to_csv(entities_csv_path, index=False)\n", "        print(f\"Entities CSV saved: {entities_csv_path.name}\")\n", "        \n", "        # Save classification-specific CSVs\n", "        for classification in processing_summary['classification_summary'].keys():\n", "            if classification != 'unknown':\n", "                classified_df = entities_df[entities_df['classification'] == classification]\n", "                if not classified_df.empty:\n", "                    classified_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_{classification}.csv\"\n", "                    classified_df.to_csv(classified_csv_path, index=False)\n", "                    print(f\"{classification.title()} entities CSV saved: {classified_csv_path.name} ({len(classified_df)} entities)\")\n", "        \n", "        # Save file statistics\n", "        file_stats_df = pd.DataFrame(all_file_stats)\n", "        file_stats_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_file_stats.csv\"\n", "        file_stats_df.to_csv(file_stats_csv_path, index=False)\n", "        print(f\"File statistics CSV saved: {file_stats_csv_path.name}\")\n", "        \n", "        # Save processing summary as JSON\n", "        summary_json_path = run_output_dir / f\"{project_type}_{site_name}_cad_summary.json\"\n", "        with open(summary_json_path, 'w') as f:\n", "            # Convert defaultdict to regular dict for JSON serialization\n", "            summary_for_json = {\n", "                'processing_summary': dict(processing_summary),\n", "                'classification_summary': dict(processing_summary['classification_summary']),\n", "                'entity_type_summary': dict(processing_summary['entity_type_summary']),\n", "                'coordinate_system': coordinate_system,\n", "                'extraction_timestamp': datetime.now().isoformat()\n", "            }\n", "            json.dump(summary_for_json, f, indent=2)\n", "        print(f\"Processing summary JSON saved: {summary_json_path.name}\")\n", "        \n", "        # Log artifacts to MLflow\n", "        mlflow.log_artifact(str(entities_csv_path))\n", "        mlflow.log_artifact(str(file_stats_csv_path))\n", "        mlflow.log_artifact(str(summary_json_path))\n", "        \n", "        # Log classification metrics\n", "        for classification, count in processing_summary['classification_summary'].items():\n", "            mlflow.log_metric(f\"entities_{classification}\", count)\n", "    \n", "    else:\n", "        print(\"\\nNo entities extracted - check file processing errors\")\n", "        mlflow.log_metric(\"extraction_success\", 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis and Validation"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Detailed Analysis ===\n", "\n", "Entity Classification Summary:\n", "  Annotation: 1019 entities (23.2%)\n", "  Electrical: 35 entities (0.8%)\n", "  Foundation: 14 entities (0.3%)\n", "  Panel: 529 entities (12.1%)\n", "  Pile: 1350 entities (30.8%)\n", "  Road: 815 entities (18.6%)\n", "  Unknown: 625 entities (14.2%)\n", "\n", "Entity Type Summary:\n", "  ACAD_PROXY_ENTITY: 1 entities (0.0%)\n", "  ACAD_TABLE: 1 entities (0.0%)\n", "  ARC: 305 entities (7.0%)\n", "  ATTDEF: 43 entities (1.0%)\n", "  CIRCLE: 28 entities (0.6%)\n", "  HATCH: 556 entities (12.7%)\n", "  IMAGE: 1 entities (0.0%)\n", "  INSERT: 706 entities (16.1%)\n", "  LINE: 1202 entities (27.4%)\n", "  LWPOLYLINE: 415 entities (9.5%)\n", "  MTEXT: 932 entities (21.2%)\n", "  OLE2FRAME: 5 entities (0.1%)\n", "  PDFREFERENCE: 1 entities (0.0%)\n", "  POLYLINE: 32 entities (0.7%)\n", "  SOLID: 22 entities (0.5%)\n", "  TEXT: 124 entities (2.8%)\n", "  TRACE: 12 entities (0.3%)\n", "  VIEWPORT: 1 entities (0.0%)\n", "\n", "Coordinate Bounds Analysis:\n", "  X range: -841.00 to 1317413.28 (span: 1318254.28)\n", "  Y range: -106.00 to 5748421.94 (span: 5748527.94)\n", "  Coordinate system: EPSG:32633\n", "\n", "Layer Analysis:\n", "  0: 1088 entities\n", "  CVT - PILE LATERAL: 330 entities\n", "  CVT_Tracker 1x52 int: 248 entities\n", "  CVT - PILE DRIVE: 240 entities\n", "  CVT - Pile END: 178 entities\n", "  SCS_Legenda: 165 entities\n", "  PDF6_Text: 160 entities\n", "  PDF4_Text: 160 entities\n", "  PDF5_Text: 160 entities\n", "  PDF_Text: 158 entities\n", "\n", "Quality Metrics:\n", "  Coordinate completeness: 100.0% (4387/4387)\n", "  Classification success rate: 85.8% (3762/4387)\n", "\n", "=== Extraction Results for Alignment and Labeling ===\n", "\n", "Pile Entities for Alignment: 1350 found\n", "Sample pile coordinates:\n", "  Pile 73C: (708120.09, 4693011.14, 0.00)\n", "  Pile 73D: (708120.09, 4693011.14, 0.00)\n", "  Pile 413F: (707289.05, 4692799.90, 0.00)\n", "  Pile 5041: (719369.58, 4694112.92, 0.00)\n", "  Pile 5042: (719369.86, 4694104.39, 0.00)\n", "\n", "Foundation Entities: 14 found\n", "\n", "Panel Entities: 529 found\n", "\n", "Text Annotations for Labeling: 1056 found\n", "Sample text annotations:\n", "  'C.CONS.' at (707711.68, 4693127.50)\n", "  'C.UTENTE' at (707710.37, 4693110.02)\n", "  'TC 3' at (707935.64, 4692740.45)\n", "\n", "=== CAD Metadata Extraction Complete ===\n", "Output directory: ../../../output_runs/cad_metadata/motali_de_castro_main_site_20250630_154333\n", "Total entities extracted: 4387\n", "Files processed successfully: 1/2\n", "\n", "Ready for downstream workflows:\n", "- Point cloud alignment using extracted coordinates\n", "- Automated labeling using classified entities\n", "- Coordinate system validation and transformation\n"]}], "source": ["# Display detailed analysis if entities were extracted\n", "if all_entities:\n", "    print(\"\\n=== Detailed Analysis ===\")\n", "    \n", "    # Classification breakdown\n", "    print(\"\\nEntity Classification Summary:\")\n", "    for classification, count in sorted(processing_summary['classification_summary'].items()):\n", "        percentage = (count / processing_summary['total_entities']) * 100\n", "        print(f\"  {classification.title()}: {count} entities ({percentage:.1f}%)\")\n", "    \n", "    # Entity type breakdown\n", "    print(\"\\nEntity Type Summary:\")\n", "    for entity_type, count in sorted(processing_summary['entity_type_summary'].items()):\n", "        percentage = (count / processing_summary['total_entities']) * 100\n", "        print(f\"  {entity_type}: {count} entities ({percentage:.1f}%)\")\n", "    \n", "    # Coordinate bounds analysis\n", "    print(\"\\nCoordinate Bounds Analysis:\")\n", "    entities_with_coords = entities_df.dropna(subset=['x_coord', 'y_coord'])\n", "    if not entities_with_coords.empty:\n", "        min_x = entities_with_coords['x_coord'].min()\n", "        max_x = entities_with_coords['x_coord'].max()\n", "        min_y = entities_with_coords['y_coord'].min()\n", "        max_y = entities_with_coords['y_coord'].max()\n", "        \n", "        print(f\"  X range: {min_x:.2f} to {max_x:.2f} (span: {max_x - min_x:.2f})\")\n", "        print(f\"  Y range: {min_y:.2f} to {max_y:.2f} (span: {max_y - min_y:.2f})\")\n", "        print(f\"  Coordinate system: {coordinate_system}\")\n", "        \n", "        # Log coordinate bounds to MLflow\n", "        mlflow.log_metric(\"coord_min_x\", min_x)\n", "        mlflow.log_metric(\"coord_max_x\", max_x)\n", "        mlflow.log_metric(\"coord_min_y\", min_y)\n", "        mlflow.log_metric(\"coord_max_y\", max_y)\n", "        mlflow.log_metric(\"coord_span_x\", max_x - min_x)\n", "        mlflow.log_metric(\"coord_span_y\", max_y - min_y)\n", "    \n", "    # Layer analysis\n", "    print(\"\\nLayer Analysis:\")\n", "    layer_counts = entities_df['layer_name'].value_counts().head(10)\n", "    for layer, count in layer_counts.items():\n", "        print(f\"  {layer}: {count} entities\")\n", "    \n", "    # Quality metrics\n", "    print(\"\\nQuality Metrics:\")\n", "    entities_with_coords_count = len(entities_with_coords)\n", "    coord_completeness = (entities_with_coords_count / len(entities_df)) * 100\n", "    print(f\"  Coordinate completeness: {coord_completeness:.1f}% ({entities_with_coords_count}/{len(entities_df)})\")\n", "    \n", "    classified_entities = len(entities_df[entities_df['classification'] != 'unknown'])\n", "    classification_rate = (classified_entities / len(entities_df)) * 100\n", "    print(f\"  Classification success rate: {classification_rate:.1f}% ({classified_entities}/{len(entities_df)})\")\n", "    \n", "    # Log quality metrics\n", "    mlflow.log_metric(\"coordinate_completeness_pct\", coord_completeness)\n", "    mlflow.log_metric(\"classification_success_pct\", classification_rate)\n", "    \n", "    print(\"\\n=== Extraction Results for Alignment and Labeling ===\")\n", "    \n", "    # Pile entities for alignment\n", "    pile_entities = entities_df[entities_df['classification'] == 'pile']\n", "    if not pile_entities.empty:\n", "        print(f\"\\nPile Entities for Alignment: {len(pile_entities)} found\")\n", "        print(\"Sample pile coordinates:\")\n", "        for idx, row in pile_entities.head(5).iterrows():\n", "            print(f\"  Pile {row['entity_id']}: ({row['x_coord']:.2f}, {row['y_coord']:.2f}, {row['z_coord']:.2f})\")\n", "    \n", "    # Foundation entities\n", "    foundation_entities = entities_df[entities_df['classification'] == 'foundation']\n", "    if not foundation_entities.empty:\n", "        print(f\"\\nFoundation Entities: {len(foundation_entities)} found\")\n", "    \n", "    # Panel entities\n", "    panel_entities = entities_df[entities_df['classification'] == 'panel']\n", "    if not panel_entities.empty:\n", "        print(f\"\\nPanel Entities: {len(panel_entities)} found\")\n", "    \n", "    # Text annotations for labeling\n", "    text_entities = entities_df[entities_df['geometry_type'] == 'text']\n", "    if not text_entities.empty:\n", "        print(f\"\\nText Annotations for Labeling: {len(text_entities)} found\")\n", "        print(\"Sample text annotations:\")\n", "        for idx, row in text_entities.head(3).iterrows():\n", "            text_content = row.get('text_content', 'N/A')\n", "            print(f\"  '{text_content}' at ({row['x_coord']:.2f}, {row['y_coord']:.2f})\")\n", "\n", "print(\"\\n=== CAD Metadata Extraction Complete ===\")\n", "print(f\"Output directory: {run_output_dir}\")\n", "print(f\"Total entities extracted: {processing_summary['total_entities']}\")\n", "print(f\"Files processed successfully: {processing_summary['successful_files']}/{processing_summary['total_files']}\")\n", "\n", "if processing_summary['total_entities'] > 0:\n", "    print(\"\\nReady for downstream workflows:\")\n", "    print(\"- Point cloud alignment using extracted coordinates\")\n", "    print(\"- Automated labeling using classified entities\")\n", "    print(\"- Coordinate system validation and transformation\")\n", "else:\n", "    print(\"\\nNo entities extracted - review file processing errors and classification rules\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Schema Conversion and Validation Functions"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def convert_to_element_metadata(cad_entities_df: pd.DataFrame, source_file: str) -> List[Dict[str, Any]]:\n", "    \"\"\"Convert CAD entities to ElementMetadata schema format.\"\"\"\n", "    \n", "    element_metadata_list = []\n", "    \n", "    for idx, row in cad_entities_df.iterrows():\n", "        # Create base element metadata following the schema\n", "        element_metadata = {\n", "            'element_id': str(row.get('entity_id', f'cad_{idx}')),\n", "            'element_name': row.get('block_name', row.get('text_content', f\"{row.get('entity_type', 'unknown')}_{idx}\")),\n", "            'element_type': map_classification_to_element_type(row.get('classification', 'unknown')),\n", "            'source_file': source_file,\n", "            'source_type': 'CAD',\n", "            \n", "            # Coordinates\n", "            'x_local': float(row.get('x_coord', 0.0)),\n", "            'y_local': float(row.get('y_coord', 0.0)),\n", "            'z_local': float(row.get('z_coord', 0.0)),\n", "            \n", "            # Transformation parameters\n", "            'rotation': float(row.get('rotation', 0.0)) if pd.notna(row.get('rotation')) else None,\n", "            'scale_x': float(row.get('scale_x', 1.0)) if pd.notna(row.get('scale_x')) else None,\n", "            'scale_y': float(row.get('scale_y', 1.0)) if pd.notna(row.get('scale_y')) else None,\n", "            'scale_z': float(row.get('scale_z', 1.0)) if pd.notna(row.get('scale_z')) else None,\n", "            \n", "            # CAD-specific attributes\n", "            'layer_name': row.get('layer_name', ''),\n", "            'entity_type': row.get('entity_type', ''),\n", "            'geometry_type': row.get('geometry_type', ''),\n", "            'classification': row.get('classification', 'unknown'),\n", "            \n", "            # Additional geometric properties\n", "            'radius': float(row.get('radius')) if pd.notna(row.get('radius')) else None,\n", "            'length': float(row.get('length')) if pd.notna(row.get('length')) else None,\n", "            'point_count': int(row.get('point_count')) if pd.notna(row.get('point_count')) else None,\n", "            'is_closed': bool(row.get('is_closed')) if pd.notna(row.get('is_closed')) else None,\n", "            \n", "            # Text content for annotations\n", "            'text_content': row.get('text_content', '') if pd.notna(row.get('text_content')) else None,\n", "            'text_height': float(row.get('text_height')) if pd.notna(row.get('text_height')) else None,\n", "            \n", "            # Metadata\n", "            'extraction_timestamp': row.get('extraction_timestamp', datetime.now().isoformat()),\n", "            'source_space': row.get('source_space', 'modelspace'),\n", "            'coordinate_system': coordinate_system,\n", "            'confidence_score': calculate_confidence_score(row),\n", "            \n", "            # Quality flags\n", "            'has_coordinates': bool(pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord'))),\n", "            'is_classified': row.get('classification', 'unknown') != 'unknown',\n", "            'is_geometric': row.get('geometry_type', '') in ['circle', 'line', 'polyline', 'insert']\n", "        }\n", "        \n", "        element_metadata_list.append(element_metadata)\n", "    \n", "    return element_metadata_list\n", "\n", "\n", "def map_classification_to_element_type(classification: str) -> str:\n", "    \"\"\"Map CAD classification to standard element types.\"\"\"\n", "    mapping = {\n", "        'pile': 'pile',\n", "        'foundation': 'foundation',\n", "        'panel': 'solar_panel',\n", "        'building': 'building',\n", "        'road': 'infrastructure',\n", "        'trench': 'infrastructure',\n", "        'electrical': 'electrical',\n", "        'annotation': 'annotation',\n", "        'unknown': 'other'\n", "    }\n", "    return mapping.get(classification.lower(), 'other')\n", "\n", "\n", "def calculate_confidence_score(row: pd.Series) -> float:\n", "    \"\"\"Calculate confidence score based on data completeness and classification.\"\"\"\n", "    score = 0.0\n", "    \n", "    # Base score for having coordinates\n", "    if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):\n", "        score += 0.3\n", "    \n", "    # Score for classification\n", "    if row.get('classification', 'unknown') != 'unknown':\n", "        score += 0.3\n", "    \n", "    # Score for having geometric properties\n", "    if row.get('geometry_type', '') in ['circle', 'line', 'polyline', 'insert']:\n", "        score += 0.2\n", "    \n", "    # Score for having layer information\n", "    if row.get('layer_name', ''):\n", "        score += 0.1\n", "    \n", "    # Score for having additional properties\n", "    if pd.notna(row.get('radius')) or pd.notna(row.get('length')) or pd.notna(row.get('text_content')):\n", "        score += 0.1\n", "    \n", "    return min(score, 1.0)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def create_alignment_ready_dataset(pile_entities: pd.DataFrame, foundation_entities: pd.DataFrame) -> Dict[str, Any]:\n", "    \"\"\"Create alignment-ready dataset with pile and foundation coordinates.\"\"\"\n", "    \n", "    alignment_data = {\n", "        'metadata': {\n", "            'dataset_type': 'cad_alignment_points',\n", "            'coordinate_system': coordinate_system,\n", "            'creation_timestamp': datetime.now().isoformat(),\n", "            'total_points': len(pile_entities) + len(foundation_entities)\n", "        },\n", "        'pile_points': [],\n", "        'foundation_points': [],\n", "        'coordinate_bounds': {\n", "            'min_x': None,\n", "            'max_x': None,\n", "            'min_y': None,\n", "            'max_y': None\n", "        }\n", "    }\n", "    \n", "    # Process pile entities\n", "    for idx, row in pile_entities.iterrows():\n", "        if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):\n", "            pile_point = {\n", "                'id': str(row.get('entity_id', f'pile_{idx}')),\n", "                'x': float(row['x_coord']),\n", "                'y': float(row['y_coord']),\n", "                'z': float(row.get('z_coord', 0.0)),\n", "                'type': 'pile',\n", "                'subtype': row.get('block_name', 'unknown'),\n", "                'layer': row.get('layer_name', ''),\n", "                'confidence': calculate_confidence_score(row)\n", "            }\n", "            alignment_data['pile_points'].append(pile_point)\n", "    \n", "    # Process foundation entities\n", "    for idx, row in foundation_entities.iterrows():\n", "        if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):\n", "            foundation_point = {\n", "                'id': str(row.get('entity_id', f'foundation_{idx}')),\n", "                'x': float(row['x_coord']),\n", "                'y': float(row['y_coord']),\n", "                'z': float(row.get('z_coord', 0.0)),\n", "                'type': 'foundation',\n", "                'subtype': row.get('block_name', 'unknown'),\n", "                'layer': row.get('layer_name', ''),\n", "                'confidence': calculate_confidence_score(row)\n", "            }\n", "            alignment_data['foundation_points'].append(foundation_point)\n", "    \n", "    # Calculate coordinate bounds\n", "    all_points = alignment_data['pile_points'] + alignment_data['foundation_points']\n", "    if all_points:\n", "        x_coords = [p['x'] for p in all_points]\n", "        y_coords = [p['y'] for p in all_points]\n", "        alignment_data['coordinate_bounds'] = {\n", "            'min_x': min(x_coords),\n", "            'max_x': max(x_coords),\n", "            'min_y': min(y_coords),\n", "            'max_y': max(y_coords)\n", "        }\n", "    \n", "    return alignment_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Schema-Compliant Outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Generating Schema-Compliant Outputs ===\n", "Element metadata saved: motali_de_castro_main_site_element_metadata.json (4387 records)\n", "Alignment dataset saved: motali_de_castro_main_site_alignment_points.json\n", "  - Pile points: 1350\n", "  - Foundation points: 14\n", "Labeling dataset saved: motali_de_castro_main_site_labeling_data.json\n", "  - Text annotations: 1056\n", "  - Classified entity types: 6\n", "\n", "=== Schema-Compliant Outputs Generated ===\n", "Ready for:\n", "- Point cloud alignment workflows\n", "- Automated labeling pipelines\n", "- Feature detection training\n", "- Coordinate system validation\n"]}], "source": ["# Generate schema-compliant outputs if entities were extracted\n", "if all_entities:\n", "    print(\"\\n=== Generating Schema-Compliant Outputs ===\")\n", "    \n", "    # Convert to element metadata schema\n", "    source_file = entities_df['source_file'].iloc[0] if not entities_df.empty else 'unknown'\n", "    element_metadata_list = convert_to_element_metadata(entities_df, source_file)\n", "    \n", "    # Save element metadata\n", "    element_metadata_path = run_output_dir / f\"{project_type}_{site_name}_element_metadata.json\"\n", "    with open(element_metadata_path, 'w') as f:\n", "        json.dump(element_metadata_list, f, indent=2, default=str)\n", "    print(f\"Element metadata saved: {element_metadata_path.name} ({len(element_metadata_list)} records)\")\n", "    \n", "    # Create alignment-ready dataset\n", "    pile_entities = entities_df[entities_df['classification'] == 'pile']\n", "    foundation_entities = entities_df[entities_df['classification'] == 'foundation']\n", "    \n", "    alignment_data = create_alignment_ready_dataset(pile_entities, foundation_entities)\n", "    alignment_path = run_output_dir / f\"{project_type}_{site_name}_alignment_points.json\"\n", "    logger.info(f\"Alignment dataset saved: {alignment_path.name}\")\n", "    \n", "    with open(alignment_path, 'w') as f:\n", "        json.dump(alignment_data, f, indent=2, default=str)\n", "    logger.info(f\"Alignment dataset saved: {alignment_path.name}\")\n", "    logger.info(f\"  - Pile points: {len(alignment_data['pile_points'])}\")\n", "    logger.info(f\"  - Foundation points: {len(alignment_data['foundation_points'])}\")\n", "    \n", "    # Create labeling dataset\n", "    labeling_data = {\n", "        'metadata': {\n", "            'dataset_type': 'cad_labeling_data',\n", "            'coordinate_system': coordinate_system,\n", "            'creation_timestamp': datetime.now().isoformat(),\n", "            'total_entities': len(entities_df)\n", "        },\n", "        'classified_entities': {},\n", "        'text_annotations': [],\n", "        'classification_summary': {}\n", "    }\n", "    \n", "    # Group entities by classification\n", "    for classification in entities_df['classification'].unique():\n", "        if classification != 'unknown':\n", "            classified_entities = entities_df[entities_df['classification'] == classification]\n", "            \n", "            entity_list = []\n", "            for idx, row in classified_entities.iterrows():\n", "                if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):\n", "                    entity = {\n", "                        'id': str(row.get('entity_id', f'{classification}_{idx}')),\n", "                        'x': float(row['x_coord']),\n", "                        'y': float(row['y_coord']),\n", "                        'z': float(row.get('z_coord', 0.0)),\n", "                        'entity_type': row.get('entity_type', ''),\n", "                        'layer': row.get('layer_name', ''),\n", "                        'confidence': calculate_confidence_score(row)\n", "                    }\n", "                    entity_list.append(entity)\n", "            \n", "            labeling_data['classified_entities'][classification] = entity_list\n", "            labeling_data['classification_summary'][classification] = len(entity_list)\n", "    \n", "    # Extract text annotations\n", "    text_entities = entities_df[entities_df['geometry_type'] == 'text']\n", "    for idx, row in text_entities.iterrows():\n", "        if pd.notna(row.get('text_content')) and pd.notna(row.get('x_coord')):\n", "            annotation = {\n", "                'id': str(row.get('entity_id', f'text_{idx}')),\n", "                'x': float(row['x_coord']),\n", "                'y': float(row['y_coord']),\n", "                'z': float(row.get('z_coord', 0.0)),\n", "                'text': str(row['text_content']),\n", "                'height': float(row.get('text_height', 0)) if pd.notna(row.get('text_height')) else None,\n", "                'rotation': float(row.get('text_rotation', 0)) if pd.notna(row.get('text_rotation')) else None,\n", "                'layer': row.get('layer_name', ''),\n", "                'classification': row.get('classification', 'annotation')\n", "            }\n", "            labeling_data['text_annotations'].append(annotation)\n", "    \n", "    # Save labeling dataset\n", "    labeling_path = run_output_dir / f\"{project_type}_{site_name}_labeling_data.json\"\n", "    with open(labeling_path, 'w') as f:\n", "        json.dump(labeling_data, f, indent=2, default=str)\n", "    logger.info(f\"Labeling dataset saved: {labeling_path.name}\")\n", "    logger.info(f\"  - Text annotations: {len(labeling_data['text_annotations'])}\")\n", "    logger.info(f\"  - Classified entity types: {len(labeling_data['classified_entities'])}\")\n", "    \n", "    # Log schema outputs to MLflow\n", "    mlflow.log_artifact(str(element_metadata_path))\n", "    mlflow.log_artifact(str(alignment_path))\n", "    mlflow.log_artifact(str(labeling_path))\n", "    \n", "    # Log schema metrics\n", "    mlflow.log_metric(\"element_metadata_records\", len(element_metadata_list))\n", "    mlflow.log_metric(\"alignment_points_total\", len(alignment_data['pile_points']) + len(alignment_data['foundation_points']))\n", "    mlflow.log_metric(\"alignment_pile_points\", len(alignment_data['pile_points']))\n", "    mlflow.log_metric(\"alignment_foundation_points\", len(alignment_data['foundation_points']))\n", "    mlflow.log_metric(\"text_annotations\", len(labeling_data['text_annotations']))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Schema-Compliant Outputs Generated\n", "Ready for:\n", "- Point cloud alignment workflows\n", "- Automated labeling pipelines\n", "- Feature detection training\n", "- Coordinate system validation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Final Summary and Validation"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "ENHANCED CAD METADATA EXTRACTION COMPLETE\n", "============================================================\n", "\n", "✅ Successfully processed 1/2 files\n", "✅ Extracted 4387 entities with 85.8% classification rate\n", "✅ Generated 1364 alignment points for point cloud registration\n", "✅ Generated 1056 text annotations for labeling\n", "\n", "📁 Output directory: ../../../output_runs/cad_metadata/motali_de_castro_main_site_20250630_154333\n", "📊 Coordinate system: EPSG:32633\n", "🕒 Processing timestamp: 20250630_154333\n", "\n", "🎯 Next Steps:\n", "1. Use alignment points for point cloud to CAD registration\n", "2. Apply classified entities for automated point cloud labeling\n", "3. Validate coordinate system consistency with survey data\n", "4. Integrate with existing preprocessing pipeline\n", "\n", "============================================================\n"]}], "source": ["# Final summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"ENHANCED CAD METADATA EXTRACTION COMPLETE\")\n", "print(\"=\"*60)\n", "\n", "if processing_summary['total_entities'] > 0:\n", "    print(f\"\\n✅ Successfully processed {processing_summary['successful_files']}/{processing_summary['total_files']} files\")\n", "    print(f\"✅ Extracted {processing_summary['total_entities']} entities with {(len(entities_df[entities_df['classification'] != 'unknown']) / len(entities_df) * 100):.1f}% classification rate\")\n", "    \n", "    if 'alignment_data' in locals():\n", "        total_alignment_points = len(alignment_data['pile_points']) + len(alignment_data['foundation_points'])\n", "        print(f\"✅ Generated {total_alignment_points} alignment points for point cloud registration\")\n", "    \n", "    if 'labeling_data' in locals():\n", "        print(f\"✅ Generated {len(labeling_data['text_annotations'])} text annotations for labeling\")\n", "    \n", "    print(f\"\\n📁 Output directory: {run_output_dir}\")\n", "    print(f\"📊 Coordinate system: {coordinate_system}\")\n", "    print(f\"🕒 Processing timestamp: {timestamp}\")\n", "    \n", "    print(\"\\n🎯 Next Steps:\")\n", "    print(\"1. Use alignment points for point cloud to CAD registration\")\n", "    print(\"2. Apply classified entities for automated point cloud labeling\")\n", "    print(\"3. Validate coordinate system consistency with survey data\")\n", "    print(\"4. Integrate with existing preprocessing pipeline\")\n", "    \n", "else:\n", "    print(\"\\n❌ No entities extracted - check input files and processing errors\")\n", "    print(\"Review file statistics and error logs for troubleshooting\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["motali_de_castro_main_site_alignment_points.json\n", "motali_de_castro_main_site_cad_annotation.csv\n", "motali_de_castro_main_site_cad_electrical.csv\n", "motali_de_castro_main_site_cad_entities.csv\n", "motali_de_castro_main_site_cad_file_stats.csv\n", "motali_de_castro_main_site_cad_foundation.csv\n", "motali_de_castro_main_site_cad_panel.csv\n", "motali_de_castro_main_site_cad_pile.csv\n", "motali_de_castro_main_site_cad_road.csv\n", "motali_de_castro_main_site_cad_summary.json\n", "motali_de_castro_main_site_element_metadata.json\n", "motali_de_castro_main_site_labeling_data.json\n"]}], "source": ["!ls ../../../output_runs/cad_metadata/motali_de_castro_main_site_20250630_154333"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
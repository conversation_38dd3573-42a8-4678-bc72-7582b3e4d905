{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhanced CAD Metadata Extraction for Alignment and Labeling\n", "\n", "This notebook provides comprehensive CAD metadata extraction from DXF and DWG files for downstream alignment and labeling workflows.\n", "\n", "**Stage**: Data Preparation - CAD Metadata Extraction  \n", "**Input Data**: DXF/DWG files from CAD directory  \n", "**Output**: Structured metadata in CSV/JSON format following metadata schema  \n", "**Purpose**: Extract geometric entities, coordinates, and annotations for point cloud alignment and labeling  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Extract comprehensive metadata from CAD files to support:\n", "- Point cloud to CAD alignment workflows\n", "- Automated labeling of point cloud features\n", "- Coordinate system transformation and validation\n", "- Geometric feature detection and classification\n", "- Foundation, pile, and infrastructure element identification"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. **File Discovery**: Locate and validate DXF/DWG files\n", "2. **Entity Extraction**: Extract geometric entities with full metadata\n", "3. **Classification**: Intelligent categorization of CAD entities\n", "4. **Coordinate Processing**: Handle coordinate systems and transformations\n", "5. **Structured Output**: Generate standardized metadata following schema\n", "6. **Quality Validation**: Verify extraction completeness and accuracy"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"motali_de_castro\"\n", "site_name = \"main_site\"\n", "cad_data_path = \"../../../data/raw/motali_de_castro/cad\"\n", "output_dir = \"../../../output_runs/cad_metadata\"\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\", \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"]\n", "coordinate_system = \"EPSG:32633\"  # UTM Zone 33N for Italy\n", "classification_rules_file = None  # Optional custom classification rules"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import logging\n", "import json\n", "import csv\n", "from pathlib import Path\n", "from datetime import datetime\n", "from typing import Dict, List, Tuple, Optional, Any\n", "from collections import defaultdict\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import ezdxf\n", "from ezdxf import recover\n", "import mlflow\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"Enhanced CAD Metadata Extraction - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"Project: {project_type}/{site_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup paths and directories\n", "cad_path = Path(cad_data_path)\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Create timestamped output directory\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "run_output_dir = output_path / f\"{project_type}_{site_name}_{timestamp}\"\n", "run_output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"CAD data path: {cad_path.resolve()}\")\n", "print(f\"Output directory: {run_output_dir.resolve()}\")\n", "\n", "# Verify CAD directory exists\n", "if not cad_path.exists():\n", "    raise FileNotFoundError(f\"CAD directory not found: {cad_path}\")\n", "\n", "logger.info(f\"Setup completed - Output directory: {run_output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced CAD Entity Classification System"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class CADEntityClassifier:\n", "    \"\"\"Enhanced classification system for CAD entities.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.classification_rules = {\n", "            'pile': {\n", "                'layer_keywords': ['pile', 'palo', 'foundation', 'fondazione'],\n", "                'block_keywords': ['pile', 'palo', 'p_', 'foundation'],\n", "                'text_keywords': ['pile', 'palo', 'p-', 'foundation'],\n", "                'entity_types': ['CIRCLE', 'INSERT', 'POINT']\n", "            },\n", "            'panel': {\n", "                'layer_keywords': ['panel', 'pv', 'solar', 'modulo', 'pannello'],\n", "                'block_keywords': ['panel', 'pv', 'solar', 'modulo'],\n", "                'text_keywords': ['panel', 'pv', 'solar'],\n", "                'entity_types': ['INSERT', 'LWPOLYLINE', 'POLYLINE']\n", "            },\n", "            'road': {\n", "                'layer_keywords': ['road', 'strada', 'strade', 'access', 'accesso'],\n", "                'block_keywords': ['road', 'strada'],\n", "                'text_keywords': ['road', 'strada'],\n", "                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE', 'ARC']\n", "            },\n", "            'trench': {\n", "                'layer_keywords': ['trench', 'trincea', 'cable', 'cavo', 'cavidotto'],\n", "                'block_keywords': ['trench', 'cable'],\n", "                'text_keywords': ['trench', 'cable', 'cavo'],\n", "                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE']\n", "            },\n", "            'foundation': {\n", "                'layer_keywords': ['foundation', 'fondazione', 'base', 'cabin', 'cabina'],\n", "                'block_keywords': ['foundation', 'cabin', 'base'],\n", "                'text_keywords': ['foundation', 'cabin', 'base'],\n", "                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT']\n", "            },\n", "            'electrical': {\n", "                'layer_keywords': ['electrical', 'elettrico', 'power', 'energia'],\n", "                'block_keywords': ['electrical', 'power'],\n", "                'text_keywords': ['electrical', 'power', 'kw', 'v'],\n", "                'entity_types': ['LINE', 'LWPOLYLINE', 'INSERT']\n", "            },\n", "            'annotation': {\n", "                'layer_keywords': ['text', 'label', 'annotation', 'quota', 'dimension'],\n", "                'block_keywords': ['text', 'label'],\n", "                'text_keywords': [],\n", "                'entity_types': ['TEXT', 'MTEXT', 'DIMENSION']\n", "            }\n", "        }\n", "    \n", "    def classify_entity(self, entity, layer_name: str = \"\", block_name: str = \"\", text_content: str = \"\") -> str:\n", "        \"\"\"Classify CAD entity based on multiple criteria.\"\"\"\n", "        entity_type = entity.dxftype()\n", "        layer_name = layer_name.lower()\n", "        block_name = block_name.lower()\n", "        text_content = text_content.lower()\n", "        \n", "        scores = defaultdict(int)\n", "        \n", "        for category, rules in self.classification_rules.items():\n", "            # Check entity type match\n", "            if entity_type in rules['entity_types']:\n", "                scores[category] += 2\n", "            \n", "            # Check layer keywords\n", "            for keyword in rules['layer_keywords']:\n", "                if keyword in layer_name:\n", "                    scores[category] += 3\n", "            \n", "            # Check block keywords\n", "            for keyword in rules['block_keywords']:\n", "                if keyword in block_name:\n", "                    scores[category] += 3\n", "            \n", "            # Check text keywords\n", "            for keyword in rules['text_keywords']:\n", "                if keyword in text_content:\n", "                    scores[category] += 2\n", "        \n", "        # Return highest scoring category or 'unknown'\n", "        if scores:\n", "            return max(scores.items(), key=lambda x: x[1])[0]\n", "        return 'unknown'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced Entity Extraction Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_comprehensive_entity_data(entity, classifier: CADEntityClassifier) -> Dict[str, Any]:\n", "    \"\"\"Extract comprehensive metadata from CAD entity.\"\"\"\n", "    try:\n", "        entity_type = entity.dxftype()\n", "        handle = getattr(entity.dxf, 'handle', 'unknown')\n", "        layer_name = getattr(entity.dxf, 'layer', '')\n", "        \n", "        # Base entity data\n", "        entity_data = {\n", "            'entity_id': handle,\n", "            'entity_type': entity_type,\n", "            'layer_name': layer_name,\n", "            'color': getattr(entity.dxf, 'color', None),\n", "            'linetype': getattr(entity.dxf, 'linetype', None),\n", "            'lineweight': getattr(entity.dxf, 'lineweight', None)\n", "        }\n", "        \n", "        # Extract geometry and coordinates based on entity type\n", "        if entity_type == 'CIRCLE':\n", "            center = entity.dxf.center\n", "            entity_data.update({\n", "                'x_coord': center[0],\n", "                'y_coord': center[1],\n", "                'z_coord': center[2] if len(center) > 2 else 0.0,\n", "                'radius': entity.dxf.radius,\n", "                'geometry_type': 'circle'\n", "            })\n", "            \n", "        elif entity_type == 'LINE':\n", "            start = entity.dxf.start\n", "            end = entity.dxf.end\n", "            entity_data.update({\n", "                'x_coord': (start[0] + end[0]) / 2,  # Midpoint\n", "                'y_coord': (start[1] + end[1]) / 2,\n", "                'z_coord': (start[2] + end[2]) / 2 if len(start) > 2 else 0.0,\n", "                'start_x': start[0],\n", "                'start_y': start[1],\n", "                'start_z': start[2] if len(start) > 2 else 0.0,\n", "                'end_x': end[0],\n", "                'end_y': end[1],\n", "                'end_z': end[2] if len(end) > 2 else 0.0,\n", "                'length': np.linalg.norm(np.array(end) - np.array(start)),\n", "                'geometry_type': 'line'\n", "            })\n", "            \n", "        elif entity_type == 'INSERT':\n", "            insert_point = entity.dxf.insert\n", "            entity_data.update({\n", "                'x_coord': insert_point[0],\n", "                'y_coord': insert_point[1],\n", "                'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,\n", "                'block_name': entity.dxf.name,\n", "                'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                'scale_x': getattr(entity.dxf, 'xscale', 1.0),\n", "                'scale_y': getattr(entity.dxf, 'yscale', 1.0),\n", "                'scale_z': getattr(entity.dxf, 'zscale', 1.0),\n", "                'geometry_type': 'insert'\n", "            })\n", "            \n", "        elif entity_type in ['TEXT', 'MTEXT']:\n", "            insert_point = entity.dxf.insert\n", "            text_content = entity.dxf.text if entity_type == 'TEXT' else entity.text\n", "            entity_data.update({\n", "                'x_coord': insert_point[0],\n", "                'y_coord': insert_point[1],\n", "                'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,\n", "                'text_content': text_content,\n", "                'text_height': getattr(entity.dxf, 'height', None),\n", "                'text_rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                'geometry_type': 'text'\n", "            })\n", "            \n", "        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:\n", "            # Get polyline points\n", "            if hasattr(entity, 'get_points'):\n", "                points = list(entity.get_points())\n", "            else:\n", "                points = []\n", "            \n", "            if points:\n", "                # Calculate centroid\n", "                points_array = np.array(points)\n", "                centroid = np.mean(points_array, axis=0)\n", "                entity_data.update({\n", "                    'x_coord': centroid[0],\n", "                    'y_coord': centroid[1],\n", "                    'z_coord': centroid[2] if len(centroid) > 2 else 0.0,\n", "                    'point_count': len(points),\n", "                    'is_closed': getattr(entity.dxf, 'flags', 0) & 1,\n", "                    'geometry_type': 'polyline'\n", "                })\n", "            else:\n", "                entity_data.update({\n", "                    'x_coord': 0.0,\n", "                    'y_coord': 0.0,\n", "                    'z_coord': 0.0,\n", "                    'geometry_type': 'polyline'\n", "                })\n", "        \n", "        else:\n", "            # Default handling for other entity types\n", "            entity_data.update({\n", "                'x_coord': 0.0,\n", "                'y_coord': 0.0,\n", "                'z_coord': 0.0,\n", "                'geometry_type': 'other'\n", "            })\n", "        \n", "        # Classify entity\n", "        block_name = entity_data.get('block_name', '')\n", "        text_content = entity_data.get('text_content', '')\n", "        entity_data['classification'] = classifier.classify_entity(\n", "            entity, layer_name, block_name, text_content\n", "        )\n", "        \n", "        # Add extraction metadata\n", "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n", "        \n", "        return entity_data\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Error extracting entity data: {e}\")\n", "        return {\n", "            'entity_id': 'error',\n", "            'entity_type': getattr(entity, 'dxftype', lambda: 'unknown')(),\n", "            'error': str(e),\n", "            'extraction_timestamp': datetime.now().isoformat()\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Processing Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_dxf_file(file_path: Path, classifier: CADEntityClassifier) -> Tuple[List[Dict], Dict]:\n", "    \"\"\"Process a single DXF file and extract all entity metadata.\"\"\"\n", "    logger.info(f\"Processing DXF file: {file_path.name}\")\n", "    \n", "    entities_data = []\n", "    file_stats = {\n", "        'file_name': file_path.name,\n", "        'file_path': str(file_path),\n", "        'file_size_mb': file_path.stat().st_size / (1024 * 1024),\n", "        'processing_timestamp': datetime.now().isoformat(),\n", "        'entity_counts': defaultdict(int),\n", "        'classification_counts': defaultdict(int),\n", "        'layer_counts': defaultdict(int),\n", "        'coordinate_bounds': {'min_x': float('inf'), 'max_x': float('-inf'),\n", "                             'min_y': float('inf'), 'max_y': float('-inf')},\n", "        'errors': []\n", "    }\n", "    \n", "    try:\n", "        # Try to read the file, use recovery if needed\n", "        try:\n", "            doc = ezdxf.readfile(file_path)\n", "        except ezdxf.DXFStructureError:\n", "            logger.warning(f\"DXF structure error, attempting recovery: {file_path.name}\")\n", "            doc, auditor = recover.readfile(file_path)\n", "            if auditor.has_errors:\n", "                file_stats['errors'].extend([str(error) for error in auditor.errors])\n", "        \n", "        # Process modelspace entities\n", "        msp = doc.modelspace()\n", "        logger.info(f\"Processing {len(msp)} entities in modelspace\")\n", "        \n", "        for entity in msp:\n", "            entity_data = extract_comprehensive_entity_data(entity, classifier)\n", "            entity_data['source_file'] = file_path.name\n", "            entity_data['source_space'] = 'modelspace'\n", "            entities_data.append(entity_data)\n", "            \n", "            # Update statistics\n", "            file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1\n", "            file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1\n", "            file_stats['layer_counts'][entity_data.get('layer_name', 'unknown')] += 1\n", "            \n", "            # Update coordinate bounds\n", "            x_coord = entity_data.get('x_coord')\n", "            y_coord = entity_data.get('y_coord')\n", "            if x_coord is not None and y_coord is not None:\n", "                file_stats['coordinate_bounds']['min_x'] = min(file_stats['coordinate_bounds']['min_x'], x_coord)\n", "                file_stats['coordinate_bounds']['max_x'] = max(file_stats['coordinate_bounds']['max_x'], x_coord)\n", "                file_stats['coordinate_bounds']['min_y'] = min(file_stats['coordinate_bounds']['min_y'], y_coord)\n", "                file_stats['coordinate_bounds']['max_y'] = max(file_stats['coordinate_bounds']['max_y'], y_coord)\n", "        \n", "        # Process block definitions\n", "        logger.info(f\"Processing {len(doc.blocks)} block definitions\")\n", "        for block in doc.blocks:\n", "            if block.name.startswith('*'):  # Skip anonymous blocks\n", "                continue\n", "                \n", "            for entity in block:\n", "                entity_data = extract_comprehensive_entity_data(entity, classifier)\n", "                entity_data['source_file'] = file_path.name\n", "                entity_data['source_space'] = f'block:{block.name}'\n", "                entities_data.append(entity_data)\n", "                \n", "                # Update statistics\n", "                file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1\n", "                file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1\n", "        \n", "        file_stats['total_entities'] = len(entities_data)\n", "        file_stats['processing_status'] = 'success'\n", "        \n", "        logger.info(f\"Successfully processed {file_path.name}: {len(entities_data)} entities\")\n", "        \n", "    except Exception as e:\n", "        error_msg = f\"Error processing {file_path.name}: {str(e)}\"\n", "        logger.error(error_msg)\n", "        file_stats['errors'].append(error_msg)\n", "        file_stats['processing_status'] = 'error'\n", "    \n", "    return entities_data, file_stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Processing Pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize classifier\n", "classifier = CADEntityClassifier()\n", "\n", "# Initialize MLflow tracking\n", "mlflow.set_experiment(f\"cad_metadata_extraction_{project_type}\")\n", "\n", "with mlflow.start_run(run_name=f\"{site_name}_cad_extraction_{timestamp}\"):\n", "    # Log parameters\n", "    mlflow.log_param(\"project_type\", project_type)\n", "    mlflow.log_param(\"site_name\", site_name)\n", "    mlflow.log_param(\"coordinate_system\", coordinate_system)\n", "    mlflow.log_param(\"target_files\", \",\".join(target_files))\n", "    \n", "    # Discover CAD files\n", "    print(\"\\n=== File Discovery ===\")\n", "    discovered_files = []\n", "    \n", "    if target_files:\n", "        # Process specific target files\n", "        for target_file in target_files:\n", "            file_path = cad_path / target_file\n", "            if file_path.exists() and file_path.suffix.lower() in ['.dxf', '.dwg']:\n", "                discovered_files.append(file_path)\n", "                print(f\"Found target file: {target_file}\")\n", "            else:\n", "                print(f\"Target file not found or invalid: {target_file}\")\n", "    else:\n", "        # Discover all DXF files in directory\n", "        for file_path in cad_path.rglob(\"*.dxf\"):\n", "            discovered_files.append(file_path)\n", "            print(f\"Discovered DXF file: {file_path.name}\")\n", "    \n", "    print(f\"\\nTotal files to process: {len(discovered_files)}\")\n", "    mlflow.log_metric(\"files_discovered\", len(discovered_files))\n", "    \n", "    # Process each file\n", "    all_entities = []\n", "    all_file_stats = []\n", "    processing_summary = {\n", "        'total_files': len(discovered_files),\n", "        'successful_files': 0,\n", "        'failed_files': 0,\n", "        'total_entities': 0,\n", "        'classification_summary': defaultdict(int),\n", "        'entity_type_summary': defaultdict(int)\n", "    }\n", "    \n", "    print(\"\\n=== Processing Files ===\")\n", "    for file_path in discovered_files:\n", "        if file_path.suffix.lower() == '.dxf':\n", "            entities_data, file_stats = process_dxf_file(file_path, classifier)\n", "            \n", "            all_entities.extend(entities_data)\n", "            all_file_stats.append(file_stats)\n", "            \n", "            if file_stats['processing_status'] == 'success':\n", "                processing_summary['successful_files'] += 1\n", "                processing_summary['total_entities'] += len(entities_data)\n", "                \n", "                # Update summaries\n", "                for classification, count in file_stats['classification_counts'].items():\n", "                    processing_summary['classification_summary'][classification] += count\n", "                for entity_type, count in file_stats['entity_counts'].items():\n", "                    processing_summary['entity_type_summary'][entity_type] += count\n", "            else:\n", "                processing_summary['failed_files'] += 1\n", "        else:\n", "            print(f\"Skipping non-DXF file: {file_path.name} (DWG processing requires conversion)\")\n", "    \n", "    print(f\"\\n=== Processing Summary ===\")\n", "    print(f\"Total files processed: {processing_summary['total_files']}\")\n", "    print(f\"Successful: {processing_summary['successful_files']}\")\n", "    print(f\"Failed: {processing_summary['failed_files']}\")\n", "    print(f\"Total entities extracted: {processing_summary['total_entities']}\")\n", "    \n", "    # Log metrics to MLflow\n", "    mlflow.log_metric(\"files_processed\", processing_summary['total_files'])\n", "    mlflow.log_metric(\"files_successful\", processing_summary['successful_files'])\n", "    mlflow.log_metric(\"files_failed\", processing_summary['failed_files'])\n", "    mlflow.log_metric(\"total_entities\", processing_summary['total_entities'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Structured Output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    # Create comprehensive DataFrame\n", "    if all_entities:\n", "        print(\"\\n=== Generating Structured Output ===\")\n", "        \n", "        # Convert to DataFrame\n", "        entities_df = pd.DataFrame(all_entities)\n", "        \n", "        # Save main entities CSV\n", "        entities_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_entities.csv\"\n", "        entities_df.to_csv(entities_csv_path, index=False)\n", "        print(f\"Entities CSV saved: {entities_csv_path.name}\")\n", "        \n", "        # Save classification-specific CSVs\n", "        for classification in processing_summary['classification_summary'].keys():\n", "            if classification != 'unknown':\n", "                classified_df = entities_df[entities_df['classification'] == classification]\n", "                if not classified_df.empty:\n", "                    classified_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_{classification}.csv\"\n", "                    classified_df.to_csv(classified_csv_path, index=False)\n", "                    print(f\"{classification.title()} entities CSV saved: {classified_csv_path.name} ({len(classified_df)} entities)\")\n", "        \n", "        # Save file statistics\n", "        file_stats_df = pd.DataFrame(all_file_stats)\n", "        file_stats_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_file_stats.csv\"\n", "        file_stats_df.to_csv(file_stats_csv_path, index=False)\n", "        print(f\"File statistics CSV saved: {file_stats_csv_path.name}\")\n", "        \n", "        # Save processing summary as JSON\n", "        summary_json_path = run_output_dir / f\"{project_type}_{site_name}_cad_summary.json\"\n", "        with open(summary_json_path, 'w') as f:\n", "            # Convert defaultdict to regular dict for JSON serialization\n", "            summary_for_json = {\n", "                'processing_summary': dict(processing_summary),\n", "                'classification_summary': dict(processing_summary['classification_summary']),\n", "                'entity_type_summary': dict(processing_summary['entity_type_summary']),\n", "                'coordinate_system': coordinate_system,\n", "                'extraction_timestamp': datetime.now().isoformat()\n", "            }\n", "            json.dump(summary_for_json, f, indent=2)\n", "        print(f\"Processing summary JSON saved: {summary_json_path.name}\")\n", "        \n", "        # Log artifacts to MLflow\n", "        mlflow.log_artifact(str(entities_csv_path))\n", "        mlflow.log_artifact(str(file_stats_csv_path))\n", "        mlflow.log_artifact(str(summary_json_path))\n", "        \n", "        # Log classification metrics\n", "        for classification, count in processing_summary['classification_summary'].items():\n", "            mlflow.log_metric(f\"entities_{classification}\", count)\n", "    \n", "    else:\n", "        print(\"\\nNo entities extracted - check file processing errors\")\n", "        mlflow.log_metric(\"extraction_success\", 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis and Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display detailed analysis if entities were extracted\n", "if all_entities:\n", "    print(\"\\n=== Detailed Analysis ===\")\n", "    \n", "    # Classification breakdown\n", "    print(\"\\nEntity Classification Summary:\")\n", "    for classification, count in sorted(processing_summary['classification_summary'].items()):\n", "        percentage = (count / processing_summary['total_entities']) * 100\n", "        print(f\"  {classification.title()}: {count} entities ({percentage:.1f}%)\")\n", "    \n", "    # Entity type breakdown\n", "    print(\"\\nEntity Type Summary:\")\n", "    for entity_type, count in sorted(processing_summary['entity_type_summary'].items()):\n", "        percentage = (count / processing_summary['total_entities']) * 100\n", "        print(f\"  {entity_type}: {count} entities ({percentage:.1f}%)\")\n", "    \n", "    # Coordinate bounds analysis\n", "    print(\"\\nCoordinate Bounds Analysis:\")\n", "    entities_with_coords = entities_df.dropna(subset=['x_coord', 'y_coord'])\n", "    if not entities_with_coords.empty:\n", "        min_x = entities_with_coords['x_coord'].min()\n", "        max_x = entities_with_coords['x_coord'].max()\n", "        min_y = entities_with_coords['y_coord'].min()\n", "        max_y = entities_with_coords['y_coord'].max()\n", "        \n", "        print(f\"  X range: {min_x:.2f} to {max_x:.2f} (span: {max_x - min_x:.2f})\")\n", "        print(f\"  Y range: {min_y:.2f} to {max_y:.2f} (span: {max_y - min_y:.2f})\")\n", "        print(f\"  Coordinate system: {coordinate_system}\")\n", "        \n", "        # Log coordinate bounds to MLflow\n", "        mlflow.log_metric(\"coord_min_x\", min_x)\n", "        mlflow.log_metric(\"coord_max_x\", max_x)\n", "        mlflow.log_metric(\"coord_min_y\", min_y)\n", "        mlflow.log_metric(\"coord_max_y\", max_y)\n", "        mlflow.log_metric(\"coord_span_x\", max_x - min_x)\n", "        mlflow.log_metric(\"coord_span_y\", max_y - min_y)\n", "    \n", "    # Layer analysis\n", "    print(\"\\nLayer Analysis:\")\n", "    layer_counts = entities_df['layer_name'].value_counts().head(10)\n", "    for layer, count in layer_counts.items():\n", "        print(f\"  {layer}: {count} entities\")\n", "    \n", "    # Quality metrics\n", "    print(\"\\nQuality Metrics:\")\n", "    entities_with_coords_count = len(entities_with_coords)\n", "    coord_completeness = (entities_with_coords_count / len(entities_df)) * 100\n", "    print(f\"  Coordinate completeness: {coord_completeness:.1f}% ({entities_with_coords_count}/{len(entities_df)})\")\n", "    \n", "    classified_entities = len(entities_df[entities_df['classification'] != 'unknown'])\n", "    classification_rate = (classified_entities / len(entities_df)) * 100\n", "    print(f\"  Classification success rate: {classification_rate:.1f}% ({classified_entities}/{len(entities_df)})\")\n", "    \n", "    # Log quality metrics\n", "    mlflow.log_metric(\"coordinate_completeness_pct\", coord_completeness)\n", "    mlflow.log_metric(\"classification_success_pct\", classification_rate)\n", "    \n", "    print(\"\\n=== Extraction Results for Alignment and Labeling ===\")\n", "    \n", "    # Pile entities for alignment\n", "    pile_entities = entities_df[entities_df['classification'] == 'pile']\n", "    if not pile_entities.empty:\n", "        print(f\"\\nPile Entities for Alignment: {len(pile_entities)} found\")\n", "        print(\"Sample pile coordinates:\")\n", "        for idx, row in pile_entities.head(5).iterrows():\n", "            print(f\"  Pile {row['entity_id']}: ({row['x_coord']:.2f}, {row['y_coord']:.2f}, {row['z_coord']:.2f})\")\n", "    \n", "    # Foundation entities\n", "    foundation_entities = entities_df[entities_df['classification'] == 'foundation']\n", "    if not foundation_entities.empty:\n", "        print(f\"\\nFoundation Entities: {len(foundation_entities)} found\")\n", "    \n", "    # Panel entities\n", "    panel_entities = entities_df[entities_df['classification'] == 'panel']\n", "    if not panel_entities.empty:\n", "        print(f\"\\nPanel Entities: {len(panel_entities)} found\")\n", "    \n", "    # Text annotations for labeling\n", "    text_entities = entities_df[entities_df['geometry_type'] == 'text']\n", "    if not text_entities.empty:\n", "        print(f\"\\nText Annotations for Labeling: {len(text_entities)} found\")\n", "        print(\"Sample text annotations:\")\n", "        for idx, row in text_entities.head(3).iterrows():\n", "            text_content = row.get('text_content', 'N/A')\n", "            print(f\"  '{text_content}' at ({row['x_coord']:.2f}, {row['y_coord']:.2f})\")\n", "\n", "print(\"\\n=== CAD Metadata Extraction Complete ===\")\n", "print(f\"Output directory: {run_output_dir}\")\n", "print(f\"Total entities extracted: {processing_summary['total_entities']}\")\n", "print(f\"Files processed successfully: {processing_summary['successful_files']}/{processing_summary['total_files']}\")\n", "\n", "if processing_summary['total_entities'] > 0:\n", "    print(\"\\nReady for downstream workflows:\")\n", "    print(\"- Point cloud alignment using extracted coordinates\")\n", "    print(\"- Automated labeling using classified entities\")\n", "    print(\"- Coordinate system validation and transformation\")\n", "else:\n", "    print(\"\\nNo entities extracted - review file processing errors and classification rules\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}
# Papermill parameters - these will be injected by Papermill
site_name = "Castro"  # Site name for output file naming
buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)
point_cloud_path = "../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply"  # Path to input point cloud file
project_type = "ENEL"  # Options: "ENEL", "USA"

# === RANSAC (Plane Segmentation) Parameters ===
# These parameters control how RANSAC detects planar surfaces in noisy point clouds.

distance_threshold = 0.2  
# Maximum distance (in meters) from a point to a candidate plane for it to be considered an inlier.
# Smaller values yield tighter fitting planes but may miss noisy or partially flat regions.

num_iterations = 1000  
# Number of random sampling iterations to attempt.
# More iterations increase the chance of finding the best-fitting plane.

min_inliers_ratio = 0.05  
# Minimum ratio of inliers (as a percentage of total points) required to accept a plane.
# Helps filter out spurious or small patch detections.

early_stop_ratio = 0.6  
# If a plane is found that covers at least this ratio of total points, RANSAC will stop early.
# Speeds up processing when large planar surfaces (e.g., ground or slabs) dominate.


# === Processing Control Parameters ===
# These help manage memory usage and performance for large point clouds.

max_points_processing = 1000000  
# Maximum number of points to process in memory at once.
# If exceeded, the point cloud should be downsampled or processed in chunks.

# Import libraries
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import logging
from pathlib import Path
from datetime import datetime
from tqdm import tqdm

# Set random seed for reproducible results
random_seed = 42                 # Random seed for reproducible results
np.random.seed(random_seed)

print(f"Libraries imported successfully. Random seed set to {random_seed}")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info(f"RANSAC Ground Segmentation initialized for site: {site_name}")
logger.info(f"Parameters - Distance threshold: {distance_threshold}m, Iterations: {num_iterations}")

# Set up paths with proper project organization
base_path = Path('../..')
data_path = base_path / 'data'
logger.info(f"Checking base data path: {data_path}, Exists: {data_path.exists()}")

# Create output directory structure for this run with method-specific naming
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_runs_path = Path('output_runs')
current_run_path = output_runs_path / f'{site_name}_ransac_{timestamp}'
current_run_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Created current run output path: {current_run_path.resolve()}")

# Input and output paths following the specified organization
if point_cloud_path:
    input_path = Path(point_cloud_path)
    logger.info(f"Custom point_cloud_path provided: {input_path}")
    if not input_path.exists():
        raise FileNotFoundError(f"Input path does not exist: {input_path}")
else:
    raw_path = data_path / project_type / site_name / 'raw'
    input_path = raw_path
    logger.info(f"Using default input path: {input_path}")

ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
ground_seg_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Input path exists: {input_path.exists()}")
logger.info(f"Ground segmentation output path exists: {ground_seg_path.exists()}")

timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
visualization_enabled = True

# Save parameters to JSON for reproducibility
parameters = {
    "run_info": {
        "timestamp": timestamp,
        "site_name": site_name,
        "project_type": project_type,
        "method": "RANSAC",
        "notebook_version": "1.1"
    },
    "ransac_parameters": {
        "distance_threshold": distance_threshold,
        "num_iterations": num_iterations,
        "min_inliers_ratio": min_inliers_ratio,
        "early_stop_ratio": early_stop_ratio
    },
    "processing_parameters": {
        "max_points_processing": max_points_processing,
        "buffer_radius": buffer_radius,
        "visualization_enabled": visualization_enabled
    },
    "paths": {
        "input_path": str(input_path),
        "output_path": str(ground_seg_path),
        "run_output_path": str(current_run_path)
    }
}

# Save parameters to file
params_file = current_run_path / "parameters.json"
with open(params_file, 'w') as f:
    json.dump(parameters, f, indent=2)

print(f"Parameters saved to: {params_file}")

# Load Point Cloud data
import open3d as o3d

# Load Point Cloud (.ply)
input_path = Path(point_cloud_path)
logger.info(f"Reading PLY file: {input_path}")

pcd = o3d.io.read_point_cloud(str(input_path))

if not pcd.has_points():
    raise ValueError("Loaded PLY file contains no points.")

points = np.asarray(pcd.points)

# Display basic statistics
logger.info(f"Point cloud statistics:")
logger.info("-" * 50)
logger.info(f"  Loaded {points.shape[0]} points")

x, y, z = points[:, 0], points[:, 1], points[:, 2]

logger.info(f"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)")
logger.info(f"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)")
logger.info(f"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)")

import numpy as np
import time
from tqdm import tqdm

logger.info(f"Running RANSAC ground detection...")
logger.info(f"Parameters: threshold={distance_threshold}m, iterations={num_iterations}, min_ratio={min_inliers_ratio}")

# Downsample the denoised point cloud before RANSAC
# Purpose: Reduce computation, avoid overfitting, and improve speed
max_points_ransac = 1_000_000
if points.shape[0] > max_points_ransac:
    logger.info(f"Downsampling point cloud from {points.shape[0]:,} to {max_points_ransac:,} points for RANSAC")
    points = points[np.random.choice(points.shape[0], size=max_points_ransac, replace=False)]

# Recompute point count AFTER downsampling
n_points = points.shape[0]
min_inliers = int(n_points * min_inliers_ratio)

best_plane_params = None
best_inliers = []
max_inliers = 0

start_time = time.time()

for i in tqdm(range(num_iterations), desc="RANSAC iterations"):
    # Randomly sample 3 points
    sample_indices = np.random.choice(n_points, 3, replace=False)
    p1, p2, p3 = points[sample_indices]

    # Compute plane normal
    v1 = p2 - p1
    v2 = p3 - p1
    normal = np.cross(v1, v2)

    norm = np.linalg.norm(normal)
    if norm < 1e-6:
        continue  # Skip degenerate planes

    normal = normal / norm

    # Enforce upward-facing normal
    if normal[2] < 0:
        normal = -normal

    # Plane equation: ax + by + cz + d = 0
    d = -np.dot(normal, p1)
    plane_params = np.append(normal, d)

    # Distance of all points to the plane
    distances = np.abs(np.dot(points, plane_params[:3]) + d)

    # Find inliers within threshold
    inliers = np.where(distances < distance_threshold)[0]
    n_inliers = len(inliers)

    if n_inliers > max_inliers and n_inliers >= min_inliers:
        best_plane_params = plane_params
        best_inliers = inliers
        max_inliers = n_inliers

        inlier_ratio = n_inliers / n_points
        if inlier_ratio > early_stop_ratio:
            print(f"Early stopping at iteration {i+1}: Found {n_inliers:,} ground points ({inlier_ratio:.2f} ratio)")
            break

end_time = time.time()

if best_plane_params is not None:
    elapsed_time = end_time - start_time
    logger.info(f"RANSAC completed in {elapsed_time:.2f} seconds")

    ground_ratio = max_inliers / n_points
    plane_eq_str = (
        f"{best_plane_params[0]:.3f}x + "
        f"{best_plane_params[1]:.3f}y + "
        f"{best_plane_params[2]:.3f}z + "
        f"{best_plane_params[3]:.3f} = 0"
    )
    summary_msg = f"Ground points: {max_inliers:,} ({ground_ratio:.2f} ratio)"
    logger.info(summary_msg)
    
    logger.info(f"Plane: {plane_eq_str}")

    ground_points = points[best_inliers]
    nonground_points = np.delete(points, best_inliers, axis=0)
else:
    raise ValueError("RANSAC failed to find a valid ground plane.")


# Save segmented point clouds with method-specific naming
import open3d as o3d

def save_ply(path, points_array, method_name=""):
    """Save point cloud with method-specific naming for comparison."""
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(path), pc)
    logger.info(f"Saved: {path}")

# Save with RANSAC method identifier
method_name = "ransac"

# Save the point clouds to the appropriate output paths
analysis_output = current_run_path / 'analysis_output'
analysis_output.mkdir(parents=True, exist_ok=True)

# Save ground and nonground points to the analysis_output directory
save_ply(analysis_output / f"{site_name}_ground.ply", ground_points)
save_ply(analysis_output / f"{site_name}_nonground.ply", nonground_points)

print(f"\nRANSAC segmentation outputs saved:")
print(f"  Ground points: {len(ground_points):,}")
print(f"  Non-ground points: {len(nonground_points):,}")
print(f"  Method identifier: {method_name}")

# Calculate ground to non-ground ratio
# Ground/Non-Ground ratio
ground_count = ground_points.shape[0]
nonground_count = nonground_points.shape[0]
total_points = ground_count + nonground_count

ground_ratio = ground_count / total_points
logger.info(f"Ground Ratio: {ground_ratio:.4f}")

nonground_ratio = nonground_count / total_points
logger.info(f"Non-Ground Ratio: {nonground_ratio:.4f}")

logger.info("-" * 50)
logger.info(f"RANSAC Segmentation Summary:")
logger.info("-" * 50)

logger.info(f"  Total points processed: {ground_points.shape[0] + nonground_points.shape[0]:,}")
logger.info(f"  Ground points: {ground_points.shape[0]:,} ({ground_ratio:.1%})")
logger.info(f"  Non-ground points: {nonground_points.shape[0]:,} ({1-ground_ratio:.1%})")

# Calculate elevation statistics for ground and non-ground points
ground_z_mean = ground_points[:, 2].mean()
ground_z_std = ground_points[:, 2].std()
nonground_z_mean = nonground_points[:, 2].mean()
nonground_z_std = nonground_points[:, 2].std()
z_separation = nonground_z_mean - ground_z_mean

logger.info(f"  RANSAC Elevation Analysis:")
logger.info("-" * 50)
logger.info(f"  Ground elevation - Mean: {ground_z_mean:.3f}m, Std: {ground_z_std:.3f}m")
logger.info(f"  Non-ground elevation - Mean: {nonground_z_mean:.3f}m, Std: {nonground_z_std:.3f}m")
logger.info(f"  Vertical separation: {z_separation:.3f}m")

# Calculate elevation ranges
ground_z_range = ground_points[:, 2].max() - ground_points[:, 2].min()
nonground_z_range = nonground_points[:, 2].max() - nonground_points[:, 2].min()
logger.info(f"  Ground elevation range: {ground_z_range:.3f}m")
logger.info(f"  Non-ground elevation range: {nonground_z_range:.3f}m")

# Bounding Box Stats
def bounding_box_stats(points):
    min_bound = np.min(points, axis=0)
    max_bound = np.max(points, axis=0)
    return max_bound - min_bound

ground_bbox = bounding_box_stats(ground_points)
nonground_bbox = bounding_box_stats(nonground_points)

logger.info("  Bounding Box Sizes (X, Y, Z):")
logger.info("-" * 50)
logger.info(f"  Ground:     {ground_bbox}")
logger.info(f"  Non-Ground: {nonground_bbox}")

# Height (Z) Distribution Plot
plt.figure(figsize=(10, 5))
plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')
plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')
plt.legend()
plt.title("Z-Height Distribution")
plt.xlabel("Z (Elevation)")
plt.ylabel("Point Count")
plt.grid(True)
plt.show()

# Final readiness print
logger.info("RANSAC Ground Segmentation - Ready!")
logger.info("=" * 50)
logger.info(f"Data path: {data_path}")
logger.info(f"Project: {project_type}/{site_name}")
logger.info(f"Input path: {input_path}")
logger.info(f"Output path: {ground_seg_path}")
logger.info(f"Current run output: {current_run_path}")
logger.info(f"RANSAC Parameters: threshold={distance_threshold}m, iterations={num_iterations}")

# Visualize the results
import open3d as o3d
import numpy as np

# Create point cloud for ground
pcd_ground = o3d.geometry.PointCloud()
pcd_ground.points = o3d.utility.Vector3dVector(ground_points)
pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green

# Create point cloud for non-ground
pcd_nonground = o3d.geometry.PointCloud()
pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)
pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red

# Show both together
o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],
                                  window_name="Ground vs Non-Ground",
                                  point_show_normal=False)

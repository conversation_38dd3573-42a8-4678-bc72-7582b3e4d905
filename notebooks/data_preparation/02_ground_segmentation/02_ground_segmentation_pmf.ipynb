# Import libraries
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import logging
from pathlib import Path
from datetime import datetime
from scipy import ndimage

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Papermill parameters - these will be injected by Papermill
site_name = "Castro"  # Site name for output file naming
buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)
point_cloud_path = "../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply"  # Path to input point cloud file
project_type = "ENEL"  # Options: "ENEL", "USA"

# === PMF (Progressive Morphological Filter) Parameters ===
# These parameters control how the morphological filter is applied to identify ground points 
# by simulating terrain smoothing across increasing window sizes.

cell_size = 1.0  
# Size of each grid cell when rasterizing the point cloud (in meters).
# Smaller values retain finer surface detail but increase computation.
# Recommended: 0.5 – 2.0 based on point density and terrain complexity.

max_window_size = 33  
# Maximum size (in raster units) of the morphological structuring element.
# Determines the scale of features that can be removed (e.g., buildings, vegetation).
# Larger values capture broader terrain variation but may oversmooth.

slope = 0.15  
# Maximum local slope (in radians) allowed during filtering.
# Points exceeding this elevation change across a window are treated as non-ground.
# Typical values: 0.1 – 0.3 for natural terrain.

max_distance = 2.5  
# Maximum elevation difference (in meters) between a point and the estimated ground surface 
# to still be classified as ground.
# Helps in removing high outliers like trees and rooftops.

initial_distance = 0.5  
# Initial threshold (in meters) for elevation difference during early filtering iterations.
# A tighter threshold avoids early misclassifications and stabilizes the progressive process.

height_threshold_ratio = 0.1  
# Proportion of the lowest height range used to seed initial ground estimation (0–1).
# Typically set between 0.05 and 0.15 to capture the base terrain while ignoring outliers.

# Set up paths with proper project organization
base_path = Path('../..')
data_path = base_path / 'data'
logger.info(f"Checking base data path: {data_path}, Exists: {data_path.exists()}")

# Create output directory structure for this run
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_runs_path = Path('output_runs')
current_run_path = output_runs_path / f'{site_name}_pmf_{timestamp}'
current_run_path.mkdir(parents=True, exist_ok=True)

# DEBUG: Confirm output run path creation
logger.info(f"Created current run output path: {current_run_path.resolve()}")

# Input and output paths following the specified organization
if point_cloud_path:
    input_path = Path(point_cloud_path)
    logger.info(f"Custom point_cloud_path provided: {input_path}")
    if not input_path.exists():
        raise FileNotFoundError(f"Input path does not exist: {input_path}")
else:
    raw_path = data_path / project_type / site_name / 'raw'
    input_path = raw_path
    logger.info(f"Using default input path: {input_path}")

ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
ground_seg_path.mkdir(parents=True, exist_ok=True)

# DEBUG: Confirm paths exist
logger.info(f"Input path exists: {input_path.exists()}")
logger.info(f"Ground segmentation output path exists: {ground_seg_path.exists()}")

# Load Point Cloud data
import open3d as o3d

# Load Point Cloud (.ply)
input_path = Path(point_cloud_path)
logger.info(f"Reading PLY file: {input_path}")

pcd = o3d.io.read_point_cloud(str(input_path))

if not pcd.has_points():
    raise ValueError("Loaded PLY file contains no points.")

points = np.asarray(pcd.points)

# Display basic statistics
logger.info(f"Point cloud statistics:")
logger.info("-" * 50)
logger.info(f"  Loaded {points.shape[0]} points")

x, y, z = points[:, 0], points[:, 1], points[:, 2]

logger.info(f"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)")
logger.info(f"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)")
logger.info(f"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)")

from scipy.ndimage import grey_erosion, grey_dilation

# Grid the point cloud (2D raster)
min_xy = np.min(points[:, :2], axis=0)
max_xy = np.max(points[:, :2], axis=0)
dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)
grid = np.full(dims, np.nan)

# Populate raster with lowest Z value per cell
for x, y, z in points:
    xi = int((x - min_xy[0]) / cell_size)
    yi = int((y - min_xy[1]) / cell_size)
    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:
        if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:
            grid[xi, yi] = z

# Fill holes
filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)

# Morphological opening (erosion then dilation)
opened = grey_dilation(grey_erosion(filled_grid, size=max_window_size), size=max_window_size)

# Ground mask based on slope threshold
z_diff = filled_grid - opened
ground_mask_2d = z_diff < slope

# Reconstruct full ground point mask
ground_mask = []
for x, y, z in points:
    xi = int((x - min_xy[0]) / cell_size)
    yi = int((y - min_xy[1]) / cell_size)
    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:
        if ground_mask_2d[xi, yi]:
            ground_mask.append(True)
        else:
            ground_mask.append(False)
ground_mask = np.array(ground_mask)

ground_points = points[ground_mask]
nonground_points = points[~ground_mask]

logger.info(f"Ground points: {ground_points.shape[0]}")
logger.info(f"Non-ground points: {nonground_points.shape[0]}")

# ## Save Output .PLY
def save_ply(path, points_array):
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(path), pc)
    logger.info(f"Saved: {path}")

# Save the point clouds to the appropriate output paths
analysis_output = current_run_path / 'analysis_output'
analysis_output.mkdir(parents=True, exist_ok=True)

# Save ground and nonground points to the analysis_output directory
save_ply(analysis_output / f"{site_name}_ground.ply", ground_points)
save_ply(analysis_output / f"{site_name}_nonground.ply", nonground_points)

# Ground/Non-Ground ratio
ground_count = ground_points.shape[0]
nonground_count = nonground_points.shape[0]
total_points = ground_count + nonground_count

ground_ratio = ground_count / total_points
logger.info(f"Ground Ratio: {ground_ratio:.4f}")

nonground_ratio = nonground_count / total_points
logger.info(f"Non-Ground Ratio: {nonground_ratio:.4f}")

ground_z_mean = ground_points[:, 2].mean()
nonground_z_mean = nonground_points[:, 2].mean()
z_separation = nonground_z_mean - ground_z_mean

logger.info("ground_z_mean:{ground_z_mean}")
logger.info(f"nonground_z_mean:{nonground_z_mean}")
logger.info(f"z_separation: {z_separation}")

# Bounding Box Stats
def bounding_box_stats(points):
    min_bound = np.min(points, axis=0)
    max_bound = np.max(points, axis=0)
    return max_bound - min_bound

ground_bbox = bounding_box_stats(ground_points)
nonground_bbox = bounding_box_stats(nonground_points)

logger.info("  Bounding Box Sizes (X, Y, Z):")
logger.info("-" * 50)
logger.info(f"  Ground:     {ground_bbox}")
logger.info(f"  Non-Ground: {nonground_bbox}")

# Height (Z) Distribution Plot
plt.figure(figsize=(10, 5))
plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')
plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')
plt.legend()
plt.title("Z-Height Distribution")
plt.xlabel("Z (Elevation)")
plt.ylabel("Point Count")
plt.grid(True)
plt.show()

# Save parameters to JSON for reproducibility
parameters = {
    "run_info": {
        "timestamp": timestamp,
        "site_name": site_name,
        "project_type": project_type,
        "method": "PMF",
        "notebook_version": "1.1"
    },
    "pmf_parameters": {
        "cell_size": cell_size,
        "max_window_size": max_window_size,
        "slope": slope,
        "max_distance": max_distance,
        "initial_distance": initial_distance,
        "height_threshold_ratio": height_threshold_ratio
    },
    "processing_parameters": {
        "buffer_radius": buffer_radius
    },
    "paths": {
        "input_path": str(input_path),
        "output_path": str(ground_seg_path),
        "run_output_path": str(current_run_path)
    }
}

# Save parameters to file
params_file = current_run_path / "parameters.json"
with open(params_file, 'w') as f:
    json.dump(parameters, f, indent=2)


# Final readiness logger.info
logger.info("PMF Ground Segmentation - Ready!")
logger.info("=" * 50)
logger.info(f"Project: {project_type}/{site_name}")
logger.info("-" * 50)
logger.info(f"Input path: {input_path}")
logger.info(f"Output path: {ground_seg_path}")
logger.info(f"Current run output: {current_run_path}")
logger.info(f"PMF Parameters: cell_size={cell_size}m, max_window={max_window_size}, slope={slope}")

# Visualize the results
import open3d as o3d
import numpy as np

# Create point cloud for ground
pcd_ground = o3d.geometry.PointCloud()
pcd_ground.points = o3d.utility.Vector3dVector(ground_points)
pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green

# Create point cloud for non-ground
pcd_nonground = o3d.geometry.PointCloud()
pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)
pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red

# Show both together
o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],
                                  window_name="Ground vs Non-Ground",
                                  point_show_normal=False)

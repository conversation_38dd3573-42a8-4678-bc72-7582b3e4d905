# Papermill parameters - these will be injected by Papermill
site_name = "Castro"  # Site name for output file naming
buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)
point_cloud_path = "../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply"  # Path to input point cloud file
project_type = "ENEL"  # Options: "ENEL", "USA"

# === CSF (Cloth Simulation Filter) Parameters ===
# These control how the cloth behaves and how the ground is identified in point cloud data.

cloth_resolution = 0.25  
# Size of each grid cell in the cloth mesh (in meters).
# Smaller values → finer cloth mesh → better detail capture but higher compute time.
# Recommended: 0.1 – 0.5 for high-resolution drone data.

max_iterations = 500  
# Maximum number of iterations for cloth simulation convergence.
# Higher values improve accuracy but slow down processing.
# Recommended: 200 – 1000 depending on scene complexity.

classification_threshold = 0.4  
# Vertical distance (in meters) used to classify a point as ground.
# Points below this threshold from the cloth surface are marked as ground.
# Tuning this helps avoid misclassifying low-lying vegetation or panels as ground.

rigidness = 3  
# Stiffness of the cloth. Values: 1 (soft) → 3 (rigid).
# Soft cloth adapts to local terrain variation (e.g., small slopes or trenches),
# while rigid cloth gives a smoother approximation of large-scale terrain.

time_step = 0.65  
# Simulation timestep size. Affects cloth stability and convergence speed.
# Should generally stay between 0.5 and 1.0.

neighbor_search_radius = 1.2  
# Radius (in meters) used to refine classification using local neighborhood context.
# Helps reduce noise in ground/non-ground labels by smoothing small misclassifications.


# Import libraries
import laspy
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import logging
from pathlib import Path
from datetime import datetime
from sklearn.neighbors import NearestNeighbors

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set up paths with proper project organization
from pathlib import Path
from datetime import datetime

base_path = Path('../..')
data_path = base_path / 'data'
logger.info(f"Checking base data path: {data_path}, Exists: {data_path.exists()}")

# Create output directory for this run
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_runs_path = Path('output_runs')
current_run_path = output_runs_path / f'{site_name}_csf_{timestamp}'
current_run_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Created output path: {current_run_path.resolve()}")

# Determine input path
if point_cloud_path:
    point_cloud_file = Path(point_cloud_path)
    if not point_cloud_file.exists():
        raise FileNotFoundError(f"Provided point_cloud_path does not exist: {point_cloud_file}")
    logger.info(f"Using provided .ply file: {point_cloud_file}")
else:
    raise ValueError("point_cloud_path must be provided as a Papermill parameter.")

# Ground segmentation output directory (organized by site/project)
ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
ground_seg_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Ground segmentation output path: {ground_seg_path.resolve()}")


import open3d as o3d

# Load Point Cloud (.ply)
input_path = Path(point_cloud_path)
logger.info(f"Reading PLY file: {input_path}")

pcd = o3d.io.read_point_cloud(str(input_path))

if not pcd.has_points():
    raise ValueError("Loaded PLY file contains no points.")

points = np.asarray(pcd.points)
logger.info(f"Loaded {points.shape[0]} points from {input_path}")


# ## Simplified CSF (Mock logic, replaceable with physics sim)
z_median = np.median(points[:, 2])
logger.info(f"Estimated ground threshold (Z median): {z_median:.2f}")

ground_mask = points[:, 2] < (z_median + classification_threshold)
ground_points = points[ground_mask]
nonground_points = points[~ground_mask]

logger.info(f"Classified {ground_points.shape[0]} ground and {nonground_points.shape[0]} non-ground points.")

# ## Save Output .PLY
def save_ply(path, points_array):
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(path), pc)
    logger.info(f"Saved: {path}")

# Save the point clouds to the appropriate output paths
analysis_output = current_run_path / 'analysis_output'
analysis_output.mkdir(parents=True, exist_ok=True)

# Save ground and nonground points to the analysis_output directory
save_ply(analysis_output / f"{site_name}_ground.ply", ground_points)
save_ply(analysis_output / f"{site_name}_nonground.ply", nonground_points)

# Ground/Non-Ground ratio
ground_count = ground_points.shape[0]
nonground_count = nonground_points.shape[0]
total_points = ground_count + nonground_count

ground_ratio = ground_count / total_points
logger.info(f"Ground Ratio: {ground_ratio:.4f}")

nonground_ratio = nonground_count / total_points
logger.info(f"Non-Ground Ratio: {nonground_ratio:.4f}")

ground_z_mean = ground_points[:, 2].mean()
nonground_z_mean = nonground_points[:, 2].mean()
z_separation = nonground_z_mean - ground_z_mean

logger.info("ground_z_mean:{ground_z_mean}")
logger.info(f"nonground_z_mean:{nonground_z_mean}")
logger.info(f"z_separation: {z_separation}")


# Bounding Box Stats
def bounding_box_stats(points):
    min_bound = np.min(points, axis=0)
    max_bound = np.max(points, axis=0)
    return max_bound - min_bound

ground_bbox = bounding_box_stats(ground_points)
nonground_bbox = bounding_box_stats(nonground_points)

logger.info("  Bounding Box Sizes (X, Y, Z):")
logger.info("-" * 50)
logger.info(f"  Ground:     {ground_bbox}")
logger.info(f"  Non-Ground: {nonground_bbox}")

# Height (Z) Distribution Plot
plt.figure(figsize=(10, 5))
plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')
plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')
plt.legend()
plt.title("Z-Height Distribution")
plt.xlabel("Z (Elevation)")
plt.ylabel("Point Count")
plt.grid(True)
plt.show()

# Final readiness logger.info
logger.info("CSF Ground Segmentation - Ready!")
logger.info("=" * 50)
logger.info(f"Project: {project_type}/{site_name}")
logger.info("-" * 50)
logger.info(f"Input path: {input_path}")
logger.info(f"Output path: {ground_seg_path}")
logger.info(f"Current run output: {current_run_path}")
logger.info(f"CSF Parameters: resolution={cloth_resolution}m, threshold={classification_threshold}m, rigidness={rigidness}")
logger.info(f"Ground points: {ground_points.shape[0]}")
logger.info(f"Non-ground points: {nonground_points.shape[0]}")
logger.info(f"Total: {ground_points.shape[0] + nonground_points.shape[0]}")


# Visualize the results
import open3d as o3d
import numpy as np

# Create point cloud for ground
pcd_ground = o3d.geometry.PointCloud()
pcd_ground.points = o3d.utility.Vector3dVector(ground_points)
pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green

# Create point cloud for non-ground
pcd_nonground = o3d.geometry.PointCloud()
pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)
pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red

# Show both together
o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],
                                  window_name="Ground vs Non-Ground",
                                  point_show_normal=False)
